

[Module]: 
Bug-Fixed: 
DetailInfo: 

# Git Commit Message Convention
#
# 第一行 Message subject，格式如下
#   <type>: <brief> 
# 
#   - <type> 须全小写；用于说明commit的类别
#   - <subject> 须以小写字母开头；用英文简要描述
# 
#   Allowed <type> values:
#     feat      新功能，不包括构建脚本
#     fix       错误修正，不包括构建脚本
#     docs      仅文档修改
#     style     不影响代码含义的修改（白字、格式化、缺少分号等）
#     refactor  重构生产代码
#     perf      优化相关，比如提升性能、体验
#     test      增加缺失的测试，重构测试；不改变生产代码
#     chore     对构建过程或辅助工具和库的改变；不改变生产代码
# 
# 第二行必须为空行
# 
# [Module]:     可以填写模块名，可不写
# Bug-Fixed:    TAPD上可直接点击复制标题与链接，没有BUG号则填写问题或需求描述
# DetailInfo:   中文较详细描述，太长建议换一行
