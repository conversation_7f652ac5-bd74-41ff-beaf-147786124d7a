plugins {
    id 'com.android.application'
}

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

android {
    namespace 'com.pfdm.screencastsource'
    compileSdk 34
    ndkVersion "27.2.12479018"

    defaultConfig {
        applicationId "com.pfdm.screencastsource"
        minSdk 29
        targetSdk 34
        versionCode 1
        versionName "2.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags '-std=c++17'
                arguments "-DMERGE_SYSTEM_NATIVE_LIBS=${project.mergeSystemNativeLibs}"
            }
        }

        ndk {
            abiFilters "arm64-v8a"
        }
    }

    signingConfigs {
        config {
            storeFile file(keystoreProperties['ReleaseStoreFile'])
            storePassword keystoreProperties['ReleaseStorePassword']
            keyAlias keystoreProperties['ReleaseKeyAlias']
            keyPassword keystoreProperties['ReleaseKeyPassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.config
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.config
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
    buildFeatures {
        viewBinding true
        aidl true
    }
    packagingOptions {
        if (project.mergeSystemNativeLibs == "false") {
            exclude '**/*.so'
            pickFirst 'lib/arm64-v8a/libscreencastsource.so'
            pickFirst 'lib/arm64-v8a/libyvrnet.so'
        }
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}