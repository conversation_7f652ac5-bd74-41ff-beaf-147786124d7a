// IScreencastService.aidl
package com.pfdm.screencastsource;
import com.pfdm.screencastsource.IScreencastListener;
// Declare any non-default types here with import statements

interface IScreencastService {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
    void setTexture(in int id, in int texture);
    void setSurface(in int id, in Surface surface);
    boolean start(in int id);
    boolean stop(in int id);
    boolean isRunning(in int id);
    void registerListener(in IScreencastListener listener);
    void unregisterListener(in IScreencastL<PERSON>ener listener);
}