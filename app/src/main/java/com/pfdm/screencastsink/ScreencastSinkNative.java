package com.pfdm.screencastsink;

import android.graphics.SurfaceTexture;
import android.view.Surface;

public class ScreencastSinkNative {

    public interface Callback {
        void onStateChanged(int id, int state);
        void onVideoSizeChanged(int id, int width, int height);
    }

    private static Callback sCallback;

    /**
     * native methods that is implemented by the 'screencastsink' native library,
     * which is packaged with this application.
     */
    public static native void init();

    public static native void clear();

    public static native void start(int id);

    public static native void stop(int id);

    public static native boolean isRunning(int id);

    public static native boolean isStreaming(int id);

    public static native void setFocused(boolean focused);

    public static native void setSurface(int id, Surface surface);

    public static native void setSurfaceTexture(int id, SurfaceTexture surfaceTexture);

    public static native void setTexture(int id, int texture);

    public static native void setVideoFrameRate(int id, int frameRate);

    public static native void setVideoFrameDropThreshold(int id, int threshold);

    public static native void setAudioFrameDropThreshold(int id, int threshold);

    public static native void setAudioSampleRate(int id, int sampleRate);

    public static native void setIp(int id, String ip);

    public static native void setStatisticsLevel(int level);

    public static native void setSourceBuildInfo(String version, long time);

    public static native void setTraceLevel(int level);

    public static native void onFrameAvailable(int id);

    public static native void clearAvailable(int id);

    public static void onStateChanged(int id, int state) {
        if (sCallback != null) {
            sCallback.onStateChanged(id, state);
        }
    }

    public static void onVideoSizeChanged(int id, int width, int height) {
        if (sCallback != null) {
            sCallback.onVideoSizeChanged(id, width, height);
        }
    }

    public static void setCallback(Callback callback) {
        sCallback = callback;
    }
}
