package com.pfdm.screencastsink;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.provider.Settings;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


public class ScreencastService extends Service {
    private static final String TAG = "ScreencastService";
    public static final int DEVICE_ID_DP = 0;
    public static final int DEVICE_ID_HDMI = 1;
    public static final int DEVICE_ID_MAX = 2;

    @IntDef({DEVICE_ID_DP, DEVICE_ID_HDMI})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DeviceId {
    }

    public static final int BOX_STATE_SLEEP = 0;
    public static final int BOX_STATE_IDLE = 1;

    public static final String NAME_HOST_ADDRESS = "box_host_address";
    public static final String NAME_DP_STREAMING_STATE = "box_dp_streaming_state";
    public static final String NAME_HDMI_STREAMING_STATE = "box_hdmi_streaming_state";
    public static final String NAME_CAST_WINDOW_STATE = "box_cast_window_state";

    public static final String NAME_STATISTICS_LEVEL = "box_statistics_level";
    public static final String NAME_STREAMING_WINDOW_STATE = "box_streaming_window_state";
    public static final String NAME_BOX_STATE = "box_state";
    public static final Uri URI_NAME_STATISTICS_LEVEL = Settings.System.getUriFor(NAME_STATISTICS_LEVEL);
    public static final Uri URI_NAME_STREAMING_WINDOW_STATE = Settings.System.getUriFor(NAME_STREAMING_WINDOW_STATE);
    public static final Uri URI_NAME_BOX_STATE = Settings.System.getUriFor(NAME_BOX_STATE);

    public static final int FLAG_STREAMING_WINDOW_INVISIBILITY = 0x00;
    public static final int FLAG_STREAMING_WINDOW_DP_VISIBILITY = 0x01;
    public static final int FLAG_STREAMING_WINDOW_HDMI_VISIBILITY = 0x02;
    public static final int FLAG_STREAMING_WINDOW_SETTINGS_VISIBILITY = 0x04;
    public static final int FLAG_STREAMING_WINDOW_FOCUSED = 0x08;
    public static final int FLAG_STREAMING_WINDOW_COVERED = 0x10;

    private final Surface[] mSurface = new Surface[DEVICE_ID_MAX];
    private final List<ScreencastListenerClient> mClients = new ArrayList<>();
    private int mLastBoxState;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }

    @SuppressLint("ForegroundServiceType")
    @Override
    public void onCreate() {
        super.onCreate();
        startForeground();
        ScreencastSinkNative.init();
        Settings.System.putInt(getContentResolver(), NAME_DP_STREAMING_STATE, 0);
        Settings.System.putInt(getContentResolver(), NAME_HDMI_STREAMING_STATE, 0);
        getContentResolver().registerContentObserver(URI_NAME_STATISTICS_LEVEL, true, mContentObserver);
        getContentResolver().registerContentObserver(URI_NAME_STREAMING_WINDOW_STATE, true, mContentObserver);
        getContentResolver().registerContentObserver(URI_NAME_BOX_STATE, true, mContentObserver);
        Log.i(TAG, "on create");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getContentResolver().unregisterContentObserver(mContentObserver);
        ScreencastSinkNative.stop(DEVICE_ID_DP);
        ScreencastSinkNative.stop(DEVICE_ID_HDMI);
        ScreencastSinkNative.clear();
        Log.i(TAG, "on destroy");
    }

    private final ContentObserver mContentObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, @Nullable Uri uri) {
            super.onChange(selfChange, uri);

            if (URI_NAME_STATISTICS_LEVEL.equals(uri)) {
                int value = Settings.System.getInt(getContentResolver(), NAME_STATISTICS_LEVEL, 0);
                Log.i(TAG, "on statistics level changed, value:" + value);
                ScreencastSinkNative.setStatisticsLevel(value);
            } else if (URI_NAME_STREAMING_WINDOW_STATE.equals(uri)) {
                int value = Settings.System.getInt(getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
                Log.i(TAG, "on streaming window state changed, value:" + Integer.toBinaryString(value));

                ScreencastSinkNative.setFocused((value & FLAG_STREAMING_WINDOW_FOCUSED) != 0);

                if ((value & FLAG_STREAMING_WINDOW_DP_VISIBILITY) != 0) {
                    if ((value & FLAG_STREAMING_WINDOW_FOCUSED) != 0) start(DEVICE_ID_DP);
                } else {
                    stop(DEVICE_ID_DP);
                }

                if ((value & FLAG_STREAMING_WINDOW_HDMI_VISIBILITY) != 0) {
                    if ((value & FLAG_STREAMING_WINDOW_FOCUSED) != 0) start(DEVICE_ID_HDMI);
                } else {
                    stop(DEVICE_ID_HDMI);
                }
            } else if (URI_NAME_BOX_STATE.equals(uri)) {
                int boxState = Settings.System.getInt(getContentResolver(), NAME_BOX_STATE, 1);
                int windowState = Settings.System.getInt(getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
                Log.i(TAG, "on box state changed, value:" + boxState + " last value:" + mLastBoxState + " window state:" + windowState);
                if (boxState <= BOX_STATE_IDLE && boxState < mLastBoxState && (windowState & FLAG_STREAMING_WINDOW_FOCUSED) == 0) {
                    stop(DEVICE_ID_DP);
                    stop(DEVICE_ID_HDMI);
                }

                mLastBoxState = boxState;
            }
        }
    };

    private final ScreencastSinkNative.Callback mSinkCallback = new ScreencastSinkNative.Callback() {
        @Override
        public void onStateChanged(int id, int state) {
            if (id == DEVICE_ID_DP) {
                Settings.System.putInt(getContentResolver(), NAME_DP_STREAMING_STATE, state);
            } else if (id == DEVICE_ID_HDMI) {
                Settings.System.putInt(getContentResolver(), NAME_HDMI_STREAMING_STATE, state);
            } else {
                Log.i(TAG, "invalid id:" + id);
            }
        }

        @Override
        public void onVideoSizeChanged(int id, int width, int height) {
            synchronized (mClients) {
                for (ScreencastListenerClient client : mClients) {
                    try {
                        client.listener.onVideoSizeChanged(id, width, height);
                    } catch (RemoteException e) {
                        Log.e(TAG, "callback onVideoSizeChanged error, client:" + client, e);
                    }
                }
            }
        }
    };

    private synchronized boolean start(int id) {
        if (ScreencastSinkNative.isRunning(id)) {
            Log.i(TAG, "screencast sink has been started, id:" + id);
            return true;
        }

        if (mSurface[id] == null) {
            Log.w(TAG, "surface is null");
            return false;
        }

        int level = Settings.System.getInt(getContentResolver(), NAME_STATISTICS_LEVEL, 0);
        Log.i(TAG, "statistics level:" + level);
        ScreencastSinkNative.setStatisticsLevel(level);

        ScreencastSinkNative.setSurface(id, mSurface[id]);
        ScreencastSinkNative.setIp(id, "************");
        ScreencastSinkNative.setCallback(mSinkCallback);

        ScreencastSinkNative.start(id);
        Log.i(TAG, "started id:" + id);
        return true;
    }

    private synchronized void stop(int id) {
        if (!ScreencastSinkNative.isRunning(id)) {
            Log.i(TAG, "screencast sink has been stopped, id:" + id);
            return;
        }

        ScreencastSinkNative.stop(id);
        Log.i(TAG, "stopped id:" + id);
    }

    private void binderDied() {
        Log.w(TAG, "client died, stop screencast");
        ScreencastSinkNative.stop(DEVICE_ID_DP);
        ScreencastSinkNative.stop(DEVICE_ID_HDMI);
        Settings.System.putInt(getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);

        Log.w(TAG, "client died, release surface");
        if (mSurface[DEVICE_ID_DP] != null) {
            mSurface[DEVICE_ID_DP].release();
            mSurface[DEVICE_ID_DP] = null;
        }

        if (mSurface[DEVICE_ID_HDMI] != null) {
            mSurface[DEVICE_ID_HDMI].release();
            mSurface[DEVICE_ID_HDMI] = null;
        }
    }

    private final IBinder mBinder = new IScreencastService.Stub() {

        @Override
        public void setTexture(int id, int texture) throws RemoteException {
            Log.i(TAG, "setTexture by process " + Binder.getCallingPid());
        }

        @Override
        public void setSurface(int id, Surface surface) throws RemoteException {
            Log.i(TAG, "setSurface by process " + Binder.getCallingPid());
            long token = Binder.clearCallingIdentity();
            mSurface[id] = surface;
            Binder.restoreCallingIdentity(token);
        }

        @Override
        public boolean start(int id) throws RemoteException {
            Log.i(TAG, "start by process " + Binder.getCallingPid());
            long token = Binder.clearCallingIdentity();
            boolean ret = ScreencastService.this.start(id);
            Binder.restoreCallingIdentity(token);
            return ret;
        }

        @Override
        public boolean stop(int id) throws RemoteException {
            Log.i(TAG, "stop by process " + Binder.getCallingPid());
            long token = Binder.clearCallingIdentity();
            ScreencastService.this.stop(id);
            Binder.restoreCallingIdentity(token);
            return true;
        }

        @Override
        public boolean isRunning(int id) throws RemoteException {
            Log.i(TAG, "isRunning by process " + Binder.getCallingPid());
            return ScreencastSinkNative.isRunning(id);
        }

        @Override
        public void registerListener(IScreencastListener listener) throws RemoteException {
            Log.i(TAG, "registerListener by process " + Binder.getCallingPid());
            final int pid = Binder.getCallingPid();
            final int uid = Binder.getCallingUid();

            long token = Binder.clearCallingIdentity();
            ScreencastService.this.registerListener(listener, pid, uid);
            Binder.restoreCallingIdentity(token);
        }

        @Override
        public void unregisterListener(IScreencastListener listener) throws RemoteException {
            Log.i(TAG, "unregisterListener by process " + Binder.getCallingPid());

            long token = Binder.clearCallingIdentity();
            ScreencastService.this.unregisterListener(listener);
            Binder.restoreCallingIdentity(token);
        }
    };

    protected void registerListener(IScreencastListener listener, int pid, int uid) {
        synchronized (mClients) {
            final ScreencastListenerClient client = new ScreencastListenerClient(this, listener, pid, uid);
            if (client.link()) {
                mClients.add(client);
            }
        }
    }

    protected void unregisterListener(IScreencastListener listener) {
        synchronized (mClients) {
            final Iterator<ScreencastListenerClient> it = mClients.iterator();
            while (it.hasNext()) {
                ScreencastListenerClient client = it.next();
                if (listener.equals(client.listener)) {
                    client.unlink();
                    it.remove();
                }
            }
        }
    }

    private static final class ScreencastListenerClient implements IBinder.DeathRecipient {
        final ScreencastService service;
        final IScreencastListener listener;
        final int pid;
        final int uid;
        final long updateTime;

        ScreencastListenerClient(ScreencastService service, IScreencastListener listener, int pid, int uid) {
            this.service = service;
            this.listener = listener;
            this.pid = pid;
            this.uid = uid;
            this.updateTime = System.currentTimeMillis();
        }

        public void binderDied() {
            Log.w(TAG, "client died, unregister listener.");
            service.unregisterListener(listener);
            service.binderDied();
        }

        boolean link() {
            Log.i(TAG, "link " + this.toString());
            try {
                listener.asBinder().linkToDeath(this, 0);
            } catch (Exception e) {
                Log.w(TAG, "Could not link to client death", e);
                return false;
            }

            return true;
        }

        void unlink() {
            Log.i(TAG, "unlink " + this.toString());
            try {
                listener.asBinder().unlinkToDeath(this, 0);
            } catch (Exception e) {
                Log.w(TAG, "Could not unlink to client death", e);
            }
        }

        @NonNull
        @Override
        public String toString() {
            return "pid:" + pid
                    + " uid:" + uid
                    + " time:" + updateTime;
        }
    }

    @SuppressLint("ForegroundServiceType")
    private void startForeground() {
        Notification notification = null;
        NotificationChannel channel = new NotificationChannel(
                "CHANNEL_ID",
                "CHANNEL_NAME",
                NotificationManager.IMPORTANCE_LOW
        );
        NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        manager.createNotificationChannel(channel);

        notification = new Notification.Builder(this, "CHANNEL_ID").build();
        startForeground(1, notification);
    }
}
