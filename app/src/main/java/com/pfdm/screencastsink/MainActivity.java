package com.pfdm.screencastsink;

import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.WindowInsetsController;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.pfdm.screencastsink.databinding.ActivityMainBinding;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_PERMISSIONS_RETURN_CODE = 1000;
    private static final String[] PERMISSIONS = {
            android.Manifest.permission.INTERNET,
    };

    public static final int DEVICE_ID_DP = 0;
    public static final int DEVICE_ID_HDMI = 1;

    private ActivityMainBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        binding.surfaceView.getHolder().addCallback(mCallback);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowInsetsController insetsController = null;
            insetsController = getWindow().getInsetsController();

            if (insetsController != null) {
                insetsController.hide(WindowInsetsController.BEHAVIOR_DEFAULT);
            }
        }

        requestPermissions();
        ScreencastSinkNative.init();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        ScreencastSinkNative.clear();
    }

    private final SurfaceHolder.Callback mCallback = new SurfaceHolder.Callback() {
        @Override
        public void surfaceCreated(@NonNull SurfaceHolder holder) {
            ScreencastSinkNative.setSurface(DEVICE_ID_DP, holder.getSurface());

            int connected = Settings.System.getInt(getContentResolver(), "box_wd_connected", 0);
            String address = Settings.System.getString(getContentResolver(), "box_host_address");
            Log.i(TAG, "connection:" + connected + " address:" + address);

            ScreencastSinkNative.setIp(DEVICE_ID_DP, address);
            ScreencastSinkNative.start(DEVICE_ID_DP);
        }

        @Override
        public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) {

        }

        @Override
        public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
            ScreencastSinkNative.stop(DEVICE_ID_DP);
        }
    };

    private void requestPermissions() {
        try {
            ActivityCompat.requestPermissions(this, PERMISSIONS, REQUEST_PERMISSIONS_RETURN_CODE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS_RETURN_CODE) {
            if (grantResults.length == 0 || grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "Permissions Denied!");
            }
        }
    }
}