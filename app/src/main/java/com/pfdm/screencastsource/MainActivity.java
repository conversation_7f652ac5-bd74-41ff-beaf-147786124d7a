package com.pfdm.screencastsource;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.opengl.GLES20;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Surface;
import android.view.TextureView;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import javax.microedition.khronos.egl.EGL10;
import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.egl.EGLContext;
import javax.microedition.khronos.egl.EGLDisplay;
import javax.microedition.khronos.egl.EGLSurface;
import android.opengl.GLES11Ext;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "CustomSurfaceExample";

    // 定义EGL常量（因为EGL10中可能没有这些常量）
    private static final int EGL_OPENGL_ES2_BIT = 0x0004;
    private static final int EGL_CONTEXT_CLIENT_VERSION = 0x3098;

    // 纹理ID和Surface相关变量
    private int mTextureId;
    private SurfaceTexture mSurfaceTexture;
    private Surface mSurface;
    private Handler mFrameHandler;

    // EGL相关变量
    private EGL10 mEgl;
    private EGLDisplay mEglDisplay;
    private EGLContext mEglContext;
    private EGLSurface mEglSurface;
    private EGLConfig mEglConfig;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化Handler用于处理帧回调
        mFrameHandler = new Handler(Looper.getMainLooper());

        // 初始化TextureView用于显示内容
        TextureView textureView = findViewById(R.id.texture_view);
        textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                // 当TextureView可用时，初始化自定义Surface
                setupCustomSurface(width, height);
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
                // 处理尺寸变化
                if (mEgl != null && mEglDisplay != null && mEglSurface != null) {
                    // 可以在这里重新设置视口等
                    GLES20.glViewport(0, 0, width, height);
                }
            }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                // 清理资源
                releaseResources();
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) {
                // 纹理更新
            }
        });
    }

    private void setupCustomSurface(int width, int height) {
        try {
            // 1. 生成OpenGL纹理
            int[] textures = new int[1];
            GLES20.glGenTextures(1, textures, 0);
            mTextureId = textures[0];

            // 绑定纹理
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, mTextureId);

            // 设置纹理参数
            GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
                    GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_LINEAR);
            GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
                    GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR);
            GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
                    GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE);
            GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES,
                    GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE);

            // 2. 创建SurfaceTexture并关联纹理
            mSurfaceTexture = new SurfaceTexture(mTextureId);

            // 3. 创建Surface
            mSurface = new Surface(mSurfaceTexture);

            // 4. 设置帧可用监听器
            mSurfaceTexture.setOnFrameAvailableListener(new SurfaceTexture.OnFrameAvailableListener() {
                @Override
                public void onFrameAvailable(SurfaceTexture surfaceTexture) {
                    // 当有新帧可用时的处理
                    Log.d(TAG, "New frame available");

                    // 更新TextureView显示
                    surfaceTexture.updateTexImage();

                    // 可以在这里通知Native层或其他组件
                    // ScreencastSinkNative.onFrameAvailable(value);
                }
            }, mFrameHandler);

            // 5. 初始化EGL环境
            initEGL(width, height);

            // 6. 开始渲染内容
            startRendering();

        } catch (Exception e) {
            Log.e(TAG, "Error setting up custom surface", e);
        }
    }

    private void initEGL(int width, int height) {
        // 初始化EGL
        mEgl = (EGL10) EGLContext.getEGL();

        // 获取显示设备
        mEglDisplay = mEgl.eglGetDisplay(EGL10.EGL_DEFAULT_DISPLAY);
        if (mEglDisplay == EGL10.EGL_NO_DISPLAY) {
            throw new RuntimeException("eglGetDisplay failed");
        }

        // 初始化EGL
        int[] version = new int[2];
        if (!mEgl.eglInitialize(mEglDisplay, version)) {
            throw new RuntimeException("eglInitialize failed");
        }

        // 选择配置
        int[] configSpec = {
                EGL10.EGL_RED_SIZE, 8,
                EGL10.EGL_GREEN_SIZE, 8,
                EGL10.EGL_BLUE_SIZE, 8,
                EGL10.EGL_ALPHA_SIZE, 8,
                EGL10.EGL_DEPTH_SIZE, 0,
                EGL10.EGL_STENCIL_SIZE, 0,
                EGL10.EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
                EGL10.EGL_NONE
        };

        EGLConfig[] configs = new EGLConfig[1];
        int[] numConfigs = new int[1];
        if (!mEgl.eglChooseConfig(mEglDisplay, configSpec, configs, 1, numConfigs)) {
            throw new RuntimeException("eglChooseConfig failed");
        }
        mEglConfig = configs[0];

        // 创建上下文
        int[] contextAttribs = {
                EGL_CONTEXT_CLIENT_VERSION, 2,
                EGL10.EGL_NONE
        };
        mEglContext = mEgl.eglCreateContext(mEglDisplay, mEglConfig, EGL10.EGL_NO_CONTEXT, contextAttribs);
        if (mEglContext == null || mEglContext == EGL10.EGL_NO_CONTEXT) {
            throw new RuntimeException("eglCreateContext failed");
        }

        // 创建EGLSurface
        int[] surfaceAttribs = {
                EGL10.EGL_NONE
        };
        mEglSurface = mEgl.eglCreateWindowSurface(mEglDisplay, mEglConfig, mSurface, surfaceAttribs);
        if (mEglSurface == null || mEglSurface == EGL10.EGL_NO_SURFACE) {
            throw new RuntimeException("eglCreateWindowSurface failed");
        }

        // 设置当前上下文
        if (!mEgl.eglMakeCurrent(mEglDisplay, mEglSurface, mEglSurface, mEglContext)) {
            throw new RuntimeException("eglMakeCurrent failed");
        }

        // 设置视口
        GLES20.glViewport(0, 0, width, height);
    }

    private void startRendering() {
        // 创建一个线程来渲染内容到Surface
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (mEgl != null && mEglDisplay != null && mEglSurface != null) {
                    try {
                        // 渲染一帧
                        renderFrame();

                        // 交换缓冲区
                        if (!mEgl.eglSwapBuffers(mEglDisplay, mEglSurface)) {
                            int error = mEgl.eglGetError();
                            Log.e(TAG, "eglSwapBuffers failed: " + error);
                            break;
                        }

                        // 控制帧率
                        Thread.sleep(16); // 约60fps
                    } catch (Exception e) {
                        Log.e(TAG, "Error in rendering thread", e);
                        break;
                    }
                }
            }
        }).start();
    }

    private void renderFrame() {
        // 简单的OpenGL渲染 - 清除屏幕并绘制一个渐变背景
        long time = System.currentTimeMillis();
        float r = (float) (0.5 + 0.5 * Math.sin(time / 1000.0));
        float g = (float) (0.5 + 0.5 * Math.sin(time / 1000.0 + 2));
        float b = (float) (0.5 + 0.5 * Math.sin(time / 1000.0 + 4));

        GLES20.glClearColor(r, g, b, 1.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);

        // 这里可以添加更复杂的渲染代码
    }

    private void releaseResources() {
        // 释放资源
        if (mEgl != null) {
            if (mEglDisplay != null) {
                mEgl.eglMakeCurrent(mEglDisplay, EGL10.EGL_NO_SURFACE, EGL10.EGL_NO_SURFACE, EGL10.EGL_NO_CONTEXT);
                if (mEglSurface != null) {
                    mEgl.eglDestroySurface(mEglDisplay, mEglSurface);
                    mEglSurface = null;
                }
                if (mEglContext != null) {
                    mEgl.eglDestroyContext(mEglDisplay, mEglContext);
                    mEglContext = null;
                }
                mEgl.eglTerminate(mEglDisplay);
                mEglDisplay = null;
            }
            mEgl = null;
        }

        if (mSurface != null) {
            mSurface.release();
            mSurface = null;
        }

        if (mSurfaceTexture != null) {
            mSurfaceTexture.release();
            mSurfaceTexture = null;
        }

        if (mTextureId != 0) {
            int[] textures = new int[]{mTextureId};
            GLES20.glDeleteTextures(1, textures, 0);
            mTextureId = 0;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        releaseResources();
    }
}
