package com.pfdm.screencastsource;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.text.TextUtils;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;

import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

public class ScreenCastService extends Service {
    private static final String TAG = "ScreenCastSource";

    static {
        System.loadLibrary("screencastsource");
    }

    public static final String ACTION_DIGITAL_SIGNAL_PLUG_STATE_CHANGED = "com.pfdm.action.DIGITAL_SIGNAL_AUDIO_STATE_CHANGED";
    public static final String NAME_DP_STREAMING_STATE = "box_dp_streaming_state";
    public static final String NAME_HDMI_STREAMING_STATE = "box_hdmi_streaming_state";
    private static final int STREAMER_FLAG_UNKNOWN = 0;
    private static final int STREAMER_FLAG_STREAMING_AUDIO = 1;
    private static final int STREAMER_FLAG_STREAMING_VIDEO = 1 << 1;
    private static final int STREAMER_FLAG_RECORD_MEDIA = 1 << 2;
    private static final int STREAMER_FLAG_RECORD_AUDIO = 1 << 3;
    private static final int STREAMER_FLAG_RECORD_VIDEO = 1 << 4;


    @IntDef({STREAMER_FLAG_UNKNOWN, STREAMER_FLAG_STREAMING_AUDIO, STREAMER_FLAG_STREAMING_VIDEO, STREAMER_FLAG_RECORD_MEDIA, STREAMER_FLAG_RECORD_AUDIO, STREAMER_FLAG_RECORD_VIDEO})
    @Retention(RetentionPolicy.SOURCE)
    public @interface StreamerFlag {
    }

    private static final String CMD_START = "start";
    private static final String CMD_STOP = "stop";
    private static final String CMD_PARAMETER = "parameter";
    private static final String CMD_PARAMETER_STREAM = "stream";
    private static final String CMD_PARAMETER_STREAM_TYPE = "type";
    private static final String CMD_PARAMETER_RECORD = "record";
    private static final String CMD_PARAMETER_RECORD_TYPE = "type";
    private static final String CMD_CONFIGURE = "configure";
    private static final String CMD_CONFIGURE_VIDEO_BIT_RATE = "video-bit-rate";
    private static final String CMD_CONFIGURE_VIDEO_FRAME_RATE = "video-frame-rate";
    private static final String CMD_CONFIGURE_VIDEO_CODEC = "video-codec";
    private static final String CMD_CONFIGURE_AUDIO_BIT_RATE = "audio-bit-rate";
    private static final String CMD_CONFIGURE_AUDIO_CHANNEL = "audio-channel";
    private static final String CMD_CONFIGURE_AUDIO_CODEC = "audio-codec";
    private static final String CMD_CONFIGURE_RUNTIME_MODE = "runtime-mode";
    private static final String CMD_SAVE_AUDDAT = "saveauddat";
    private static final String CMD_SAVE_AUDPCM = "saveaudpcm";
    private static final String CMD_SAVE_VIDDAT = "saveviddat";
    private static final String CMD_SAVE_VIDYUV = "savevidyuv";
    private static final String CMD_SET_LEVEL = "level";

    private final Handler mHandler = new Handler();
    private PowerManager.WakeLock mWakeLock;

    private final Configure[] mConfigures = {
            new Configure(),
            new Configure()
    };

    enum DeviceState {
        DEVICE_STATE_PLUG_OUT, DEVICE_STATE_PLUG_IN
    }

    public static final String NAME_STATISTICS_LEVEL = "box_statistics_level";
    public static final Uri URI_NAME_STATISTICS_LEVEL = Settings.System.getUriFor(NAME_STATISTICS_LEVEL);

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "screen cast service start.");
        nativeInit();

        Settings.System.putInt(getContentResolver(), NAME_DP_STREAMING_STATE, 0);
        Settings.System.putInt(getContentResolver(), NAME_HDMI_STREAMING_STATE, 0);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_DIGITAL_SIGNAL_PLUG_STATE_CHANGED);

        registerReceiver(mReceiver, intentFilter, Context.RECEIVER_EXPORTED);
        getContentResolver().registerContentObserver(URI_NAME_STATISTICS_LEVEL, true, mContentObserver);

        startForeground();
        nativeSetProcessAffinity();

        mConfigures[0].id = 0;
        mConfigures[0].deviceId = Configure.DEVICE_ID_DP;
        mConfigures[0].videoBitRate = 30 * 1024 * 1024;
        mConfigures[0].videoFrameRate = 60;
        mConfigures[0].videoColorFormat = 0;
        mConfigures[0].videoCodec = Configure.VIDEO_CODEC_H265;
        mConfigures[0].audioBitRate = 163840;
        mConfigures[0].audioChannelCount = 2;
        mConfigures[0].audioCodec = Configure.AUDIO_CODEC_PCM;
        mConfigures[0].port = 1888;
        mConfigures[0].runtimeMode = Configure.RUNTIME_MODE_NORMAL;

        mConfigures[1].id = 1;
        mConfigures[1].deviceId = Configure.DEVICE_ID_HDMI;
        mConfigures[1].videoBitRate = 30 * 1024 * 1024;
        mConfigures[1].videoFrameRate = 60;
        mConfigures[1].videoColorFormat = 0;
        mConfigures[1].videoCodec = Configure.VIDEO_CODEC_H265;
        mConfigures[1].audioBitRate = 163840;
        mConfigures[1].audioChannelCount = 2;
        mConfigures[1].audioCodec = Configure.AUDIO_CODEC_PCM;
        mConfigures[1].port = 1896;
        mConfigures[1].runtimeMode = Configure.RUNTIME_MODE_NORMAL;

        for (Configure configure : mConfigures) {
            configure(configure.id, configure);
        }

        PowerManager pm = (PowerManager) this.getSystemService(Context.POWER_SERVICE);
        mWakeLock = pm.newWakeLock(PowerManager.ON_AFTER_RELEASE | PowerManager.PARTIAL_WAKE_LOCK, "screen_cast_source:wake_lock");
        mWakeLock.setReferenceCounted(false);

        Log.i(TAG, "on create");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(mReceiver);
        getContentResolver().unregisterContentObserver(mContentObserver);

        stop(Configure.DEVICE_ID_DP);
        stop(Configure.DEVICE_ID_HDMI);
        nativeClear();

        if (mWakeLock != null) {
            mWakeLock.release();
        }

        Log.i(TAG, "on destroy");
    }

    private final ContentObserver mContentObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, @Nullable Uri uri) {
            super.onChange(selfChange, uri);

            if (URI_NAME_STATISTICS_LEVEL.equals(uri)) {
                int value = Settings.System.getInt(getContentResolver(), NAME_STATISTICS_LEVEL, 0);
                Log.i(TAG, "on statistics level changed, value:" + value);
                nativeSetStatisticsLevel(value);
            }
        }
    };

    private void startForeground() {
        Notification notification = null;
        NotificationChannel channel = new NotificationChannel(
                "CHANNEL_ID",
                "CHANNEL_NAME",
                NotificationManager.IMPORTANCE_LOW
        );
        NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        manager.createNotificationChannel(channel);

        notification = new Notification.Builder(this, "CHANNEL_ID").build();
        startForeground(1, notification);
    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int type = intent.getIntExtra("type", 0);
            int state = intent.getIntExtra("state", 0);
            final int idn = clamp(type, Configure.DEVICE_ID_DP, Configure.DEVICE_ID_HDMI);
            Log.i(TAG, "on receive type:" + type + " state:" + state + " id:" + idn);

            if (mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_NORMAL || mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_MANUAL) {
                return;
            }

            if (state == DeviceState.DEVICE_STATE_PLUG_IN.ordinal()) {
                if (mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_AUTO) {
                    start(idn);
                } else if (mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_FACTORY) {
                    start(idn, STREAMER_FLAG_RECORD_MEDIA);

                    if (idn == Configure.DEVICE_ID_DP) {
                        mHandler.postDelayed(() -> {
                            Path inputFilePath = Paths.get("/sys/class/power_supply/anx7411/current_max");
                            Path outputFilePath = Paths.get("/sdcard/current_max");

                            try {
                                byte[] bytes = Files.readAllBytes(inputFilePath);
                                Log.i(TAG, "read:" + new String(bytes));

                                Files.write(outputFilePath, bytes);
                            } catch (IOException e) {
                                Log.e(TAG, "get current max error", e);
                            }
                        }, 3 * 1000);
                    }

                    mHandler.postDelayed(() -> {
                        stop(idn);
                    }, 11 * 1000);
                }

            } else {
                stop(idn);
            }
        }
    };

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        try {
            if (intent != null) {
                String cmd = intent.getStringExtra("cmd");
                String id = intent.getStringExtra("id");
                Log.i(TAG, "intent cmd:" + cmd + " id:" + id);

                int idn = clamp(TextUtils.isEmpty(id) ? 0 : Integer.parseInt(id), Configure.DEVICE_ID_DP, Configure.DEVICE_ID_HDMI);
                if (mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_FACTORY && !TextUtils.isEmpty(intent.getPackage())) {
                    Log.w(TAG, "factory mode, can not start command");
                    return super.onStartCommand(intent, flags, startId);
                }

                if (mConfigures[idn].runtimeMode == Configure.RUNTIME_MODE_MANUAL && !TextUtils.isEmpty(intent.getPackage())) {
                    Log.w(TAG, "manual mode, can not start command");
                    return super.onStartCommand(intent, flags, startId);
                }

                if (CMD_START.equals(cmd)) {
                    String parameter = intent.getStringExtra(CMD_PARAMETER);
                    if (CMD_PARAMETER_RECORD.equals(parameter)) {
                        int flag = STREAMER_FLAG_UNKNOWN;
                        String type = intent.getStringExtra(CMD_PARAMETER_RECORD_TYPE);
                        if (!TextUtils.isEmpty(type)) {
                            if (type.contains("audio")) {
                                flag |= STREAMER_FLAG_RECORD_AUDIO;
                            }
                            if (type.contains("video")) {
                                flag |= STREAMER_FLAG_RECORD_VIDEO;
                            }
                            if (type.contains("media")) {
                                flag |= STREAMER_FLAG_RECORD_MEDIA;
                            }
                        }

                        start(idn, flag == STREAMER_FLAG_UNKNOWN ? STREAMER_FLAG_RECORD_MEDIA : flag);
                    } else if (CMD_PARAMETER_STREAM.equals(parameter)) {
                        int flag = STREAMER_FLAG_UNKNOWN;
                        String type = intent.getStringExtra(CMD_PARAMETER_STREAM_TYPE);
                        if (!TextUtils.isEmpty(type)) {
                            if (type.contains("audio")) {
                                flag |= STREAMER_FLAG_STREAMING_AUDIO;
                            }
                            if (type.contains("video")) {
                                flag |= STREAMER_FLAG_STREAMING_VIDEO;
                            }
                        }

                        if (flag == STREAMER_FLAG_UNKNOWN) start(idn);
                        else start(idn, flag);
                    } else {
                        start(idn);
                    }
                } else if (CMD_STOP.equals(cmd)) {
                    String parameter = intent.getStringExtra(CMD_PARAMETER);
                    if (CMD_PARAMETER_RECORD.equals(parameter)) {
                        stop(idn, STREAMER_FLAG_UNKNOWN);
                    } else if (CMD_PARAMETER_STREAM.equals(parameter)) {
                        int flag = STREAMER_FLAG_UNKNOWN;
                        String type = intent.getStringExtra(CMD_PARAMETER_STREAM_TYPE);
                        if (!TextUtils.isEmpty(type)) {
                            if (type.contains("audio")) {
                                flag |= STREAMER_FLAG_STREAMING_AUDIO;
                            }
                            if (type.contains("video")) {
                                flag |= STREAMER_FLAG_STREAMING_VIDEO;
                            }
                        }
                        if (flag == STREAMER_FLAG_UNKNOWN) stop(idn);
                        else stop(idn, flag);
                    } else {
                        stop(idn);
                    }
                } else if (CMD_SAVE_AUDDAT.equals(cmd)) {
                    String savelevel = intent.getStringExtra(CMD_SET_LEVEL);
                    if (!TextUtils.isEmpty(savelevel)) {
                        saveauddat(idn, Integer.parseInt(savelevel) == 0 ? false : true);
                    } else {
                        saveauddat(idn, false);
                    }
                } else if (CMD_SAVE_AUDPCM.equals(cmd)) {
                    String savelevel = intent.getStringExtra(CMD_SET_LEVEL);
                    if (!TextUtils.isEmpty(savelevel)) {
                        saveaudpcm(idn, Integer.parseInt(savelevel) == 0 ? false : true);
                    } else {
                        saveaudpcm(idn, false);
                    }
                } else if (CMD_SAVE_VIDDAT.equals(cmd)) {
                    String savelevel = intent.getStringExtra(CMD_SET_LEVEL);
                    if (!TextUtils.isEmpty(savelevel)) {
                        saveviddat(idn, Integer.parseInt(savelevel) == 0 ? false : true);
                    } else {
                        saveviddat(idn, false);
                    }
                } else if (CMD_SAVE_VIDYUV.equals(cmd)) {
                    String savelevel = intent.getStringExtra(CMD_SET_LEVEL);
                    if (!TextUtils.isEmpty(savelevel)) {
                        savevidyuv(idn, Integer.parseInt(savelevel));
                    } else {
                        savevidyuv(idn, 0);
                    }
                } else if (CMD_CONFIGURE.equals(cmd)) {
                    String videoFrameRate = intent.getStringExtra(CMD_CONFIGURE_VIDEO_FRAME_RATE);
                    if (!TextUtils.isEmpty(videoFrameRate)) {
                        mConfigures[idn].videoFrameRate = Integer.parseInt(videoFrameRate);
                    }

                    String videoBitRate = intent.getStringExtra(CMD_CONFIGURE_VIDEO_BIT_RATE);
                    if (!TextUtils.isEmpty(videoBitRate)) {
                        mConfigures[idn].videoBitRate = Integer.parseInt(videoBitRate);
                    }

                    String videoCodec = intent.getStringExtra(CMD_CONFIGURE_VIDEO_CODEC);
                    if (!TextUtils.isEmpty(videoCodec)) {
                        mConfigures[idn].videoCodec = Integer.parseInt(videoCodec);
                    }

                    String audioBitRate = intent.getStringExtra(CMD_CONFIGURE_AUDIO_BIT_RATE);
                    if (!TextUtils.isEmpty(audioBitRate)) {
                        mConfigures[idn].audioBitRate = Integer.parseInt(audioBitRate);
                    }

                    String audioChannel = intent.getStringExtra(CMD_CONFIGURE_AUDIO_CHANNEL);
                    if (!TextUtils.isEmpty(audioChannel)) {
                        mConfigures[idn].audioChannelCount = Integer.parseInt(audioChannel);
                    }

                    String audioCodec = intent.getStringExtra(CMD_CONFIGURE_AUDIO_CODEC);
                    if (!TextUtils.isEmpty(audioCodec)) {
                        mConfigures[idn].audioCodec = Integer.parseInt(audioCodec);
                    }

                    String runtimeMode = intent.getStringExtra(CMD_CONFIGURE_RUNTIME_MODE);
                    if (!TextUtils.isEmpty(runtimeMode)) {
                        mConfigures[idn].runtimeMode = Integer.parseInt(runtimeMode);
                    }

                    configure(idn, mConfigures[idn]);
                } else {
                    Log.w(TAG, "invalid cmd:" + cmd + " id:" + id);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "on start command error", e);
        }

        return super.onStartCommand(intent, flags, startId);
    }

    public int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }

    private void configure(int id, Configure configure) {
        Log.i(TAG, "configure:" + configure);
        nativeConfigure(id, configure);
    }

    private void start(int id) {
        @StreamerFlag int flag = STREAMER_FLAG_UNKNOWN;
        flag |= STREAMER_FLAG_STREAMING_AUDIO;
        flag |= STREAMER_FLAG_STREAMING_VIDEO;

        start(id, flag);
    }

    private void start(int id, @StreamerFlag int flag) {
        Log.i(TAG, "start id:" + id + " flag:" + Integer.toBinaryString(flag));
        if (mWakeLock != null && !mWakeLock.isHeld()) {
            mWakeLock.acquire();
            Log.i(TAG, "acquire wake lock");
        }

        int level = Settings.System.getInt(getContentResolver(), NAME_STATISTICS_LEVEL, 0);
        nativeSetStatisticsLevel(level);
        nativeStart(id, flag);
    }

    private void stop(int id) {
        @StreamerFlag int flag = STREAMER_FLAG_UNKNOWN;
        flag |= STREAMER_FLAG_STREAMING_AUDIO;
        flag |= STREAMER_FLAG_STREAMING_VIDEO;

        stop(id, flag);
    }

    private void stop(int id, @StreamerFlag int flag) {
        Log.i(TAG, "stop id:" + id + " flag:" + Integer.toBinaryString(flag));
        nativeStop(id, flag);

        if (!nativeIsRunning(Configure.DEVICE_ID_DP) && !nativeIsRunning(Configure.DEVICE_ID_HDMI)) {
            if (mWakeLock != null && mWakeLock.isHeld()) {
                mWakeLock.release();
                Log.i(TAG, "release wake lock");
            }
        }
    }

    public void notifyStreamingStateChanged(int id, int state) {
        Log.i(TAG, "notify streaming state changed, id:" + id + " state:" + state);

        if (id == Configure.DEVICE_ID_DP) {
            Settings.System.putInt(getContentResolver(), NAME_DP_STREAMING_STATE, state);
        } else if (id == Configure.DEVICE_ID_HDMI) {
            Settings.System.putInt(getContentResolver(), NAME_HDMI_STREAMING_STATE, state);
        } else {
            Log.i(TAG, "invalid id:" + id);
        }
    }

    private native void nativeInit();

    private native void nativeClear();

    private native void nativeConfigure(int id, Configure configure);

    private native void nativeStart(int id, int flag);

    private native void nativeStop(int id, int flag);

    private native boolean nativeIsRunning(int id);

    private native boolean nativeIsStreaming(int id);

    private native void nativeSetProcessAffinity();

    private native void nativeSetStatisticsLevel(int level);

    private native void saveauddat(int id, boolean bsave);

    private native void saveaudpcm(int id, boolean bsave);

    private native void saveviddat(int id, boolean bsave);

    private native void savevidyuv(int id, int savenum);
}
