package com.pfdm.screencastsource;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class Configure {

    public static final int DEVICE_ID_DP = 0;
    public static final int DEVICE_ID_HDMI = 1;

    @IntDef({DEVICE_ID_DP, DEVICE_ID_HDMI})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DeviceId {
    }

    public static final int AUDIO_CODEC_UNKNOWN = 0;
    public static final int AUDIO_CODEC_AAC = 1;
    public static final int AUDIO_CODEC_PCM = 2;

    @IntDef({AUDIO_CODEC_UNKNOWN, AUDIO_CODEC_AAC, AUDIO_CODEC_PCM})
    @Retention(RetentionPolicy.SOURCE)
    public @interface AudioCodec {
    }

    public static final int VIDEO_CODEC_UNKNOWN = 0;
    public static final int VIDEO_CODEC_H264 = 1;
    public static final int VIDEO_CODEC_H265 = 2;

    @IntDef({VIDEO_CODEC_UNKNOWN, VIDEO_CODEC_H264, VIDEO_CODEC_H265})
    @Retention(RetentionPolicy.SOURCE)
    public @interface VideoCodec {
    }

    public static final int RUNTIME_MODE_NORMAL = 0;
    public static final int RUNTIME_MODE_AUTO = 1;
    public static final int RUNTIME_MODE_FACTORY = 2;
    public static final int RUNTIME_MODE_MANUAL = 3;

    @IntDef({RUNTIME_MODE_NORMAL, RUNTIME_MODE_AUTO, RUNTIME_MODE_FACTORY, RUNTIME_MODE_MANUAL})
    @Retention(RetentionPolicy.SOURCE)
    public @interface RuntimeMode {
    }


    public int id;
    public @DeviceId int deviceId;
    public int videoBitRate;
    public int videoFrameRate;
    public int videoColorFormat;
    public @VideoCodec int videoCodec;
    public int audioBitRate;
    public int audioChannelCount;
    public @AudioCodec int audioCodec;
    public int port;
    public @RuntimeMode int runtimeMode;

    @NonNull
    @Override
    public String toString() {
        return "id:" + id
                + " deviceId:" + deviceId
                + " videoBitRate:" + videoBitRate
                + " videoFrameRate:" + videoFrameRate
                + " videoColorFormat:" + videoColorFormat
                + " videoCodec:" + videoCodec
                + " audioBitRate:" + audioBitRate
                + " audioChannelCount:" + audioChannelCount
                + " audioCodec:" + audioCodec
                + " port:" + port
                + " runtimeMode:" + runtimeMode;
    }
}
