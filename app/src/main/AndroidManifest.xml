<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        tools:targetApi="34">

        <service
            android:name=".ScreenCastService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="specialUse"
            tools:ignore="ForegroundServicePermission">
            <intent-filter>
                <action android:name="com.pfdm.intent.action.SCREENCAST_SOURCE" />
            </intent-filter>
        </service>

        <activity
            android:name="com.pfdm.screencastsink.MainActivity"
            android:exported="true"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name="com.pfdm.screencastsink.ScreencastService"
            android:enabled="true"
            android:exported="true"
            tools:ignore="ForegroundServicePermission">
            <intent-filter>
                <action android:name="com.pfdm.intent.action.SCREENCAST_SINK" />
            </intent-filter>
        </service>
        <receiver
            android:name="com.pfdm.screencastsink.ScreencastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <category android:name="android.intent.category.DEFAULT" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
    </application>

</manifest>