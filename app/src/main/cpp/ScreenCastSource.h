#ifndef SCREENCASTSOURCE_SCREENCASTSOURCE_H
#define SCREENCASTSOURCE_SCREENCASTSOURCE_H

#include <jni.h>
#include "media/Capture.h"
#include "Common.h"
#include "Streamer.h"

class ScreenCastSource {
public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onConnectionChanged(bool connected) = 0;

        virtual void onReceived(const char *data, int size) = 0;

        virtual void onReceived(const char *data, int size, int mediaType, int width, int height, void* pStatus) = 0;
    };

    class StreamerCallback : public Streamer::Callback {

        /*
         *
        public:
            explicit ScreenCastSourceCallback(MediaPlayer *player) { mPlayer = player; }
            void onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) override;
            void onConnectionChanged(bool connected) override;
            void onReceived(const char *data, int size) override;

        private:
            const MediaPlayer *mPlayer;
         */
    public:
        StreamerCallback(DeviceID id, ScreenCastSource *source) : mID(id), mSource(source) {}
        void onConnectionChanged(bool connected) override;
        void onReceived(const char *data, int size) override;
        void onReceived(const char *data, int size, int mediaType, int width, int height, void* pStatus) override;

    private:
        const DeviceID mID;
        ScreenCastSource *mSource;

    };

public:
    static ScreenCastSource &instance() {
        static ScreenCastSource pInstance;
        return pInstance;
    }

    ~ScreenCastSource();

    void init(JNIEnv *env, jobject instance);

    void clear(JNIEnv *env);

    void start(JNIEnv *env, int id, int flag);

    void stop(JNIEnv *env, int id, int flag);

    bool isRunning(int id);

    bool isStreaming(int id);

    void configure(JNIEnv *env, int id, jobject configure);

    void setStatisticsLevel(int level);

    void saveAudDat(int id, bool save);
    void saveAudPcm(int id, bool save);
    void saveVidDat(int id, bool save);
    void saveVidYuv(int id, int save);

    StreamerCallback &getSteamerCallback(DeviceID id);
    void setCallback(ScreenCastSource::Callback *callback);

protected:
    void onConnectionChanged(DeviceID id, bool connected);

private:
    ScreenCastSource();

    int getDeviceWidth(int id);

    int getDeviceHeight(int id);

    int getDeviceFrameRate(int id);

    bool isVideoDeviceActivated(int id);

    bool isAudioDeviceActivated(int id);

    int getDeviceSampleRate(int id);

private:
    AppContext mContext;
    Streamer::Config mConfigs[DEVICE_ID_MAX];
    Streamer mStreamer[DEVICE_ID_MAX];
    StreamerCallback *mDPCallback;
    StreamerCallback *mHDMICallback;
    jmethodID mNotifyStreamingStateChangedMethod;
    ScreenCastSource::Callback *mCallback;
};


#endif //SCREENCASTSOURCE_SCREENCASTSOURCE_H
