#include <fcntl.h>
#include <unistd.h>
#include "Streamer.h"
#include "utils/Utils.hpp"

Streamer::Streamer() {
    mAudIndex = 0;
    mVidIndex = 0;
    mMediaCapture = std::make_shared<MediaCapture>();
    mMediaCapture->setCallback(this);
    //mStreamConnector = std::make_shared<StreamConnector>();
    //mStreamConnector->setCallback(this);
    mFlag = STREAMER_FLAG_UNKNOWN;
    mRecorder = std::make_shared<Streamer::Recorder>(this);
    mCallback = nullptr;
    mConnected = false;
}

Streamer::~Streamer() {
    mMediaCapture->setCallback(nullptr);
    //mStreamConnector->setCallback(nullptr);
}

void Streamer::configure(Streamer::Config &config) {
    mConfig = config;
}

void Streamer::start(StreamerFlag flag) {
    std::lock_guard<std::mutex> lock(mMutex);
    LOGI("[%d]streamer start", mConfig.id);

    mMediaCapture->configure(mConfig.captureConfig);

    /*if (!mStreamConnector->isRunning()) {
        LOG("[%d]start stream connector", mConfig.id);
        mStreamConnector->configure(mConfig.connectorConfig);
        mStreamConnector->start();
    }*/

    if ((flag & STREAMER_FLAG_STREAMING_VIDEO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        if (!mMediaCapture->isVideoCapturing()) {
            Statistics::Instance().stop(mConfig.id, DECODER_VIDEO);
            Statistics::Instance().setMaxFrame(mConfig.id, DECODER_VIDEO, VIDEO_BUFFER_SIZE);
            //Statistics::Instance().setNetDevice(mConfig.id, DECODER_VIDEO, mStreamConnector->getVidConnector());
            mVidIndex = 0;

            mMediaCapture->startVideo();
            LOGI("[%d]streamer video started", mConfig.id);
        } else {
            LOGI("[%d]streamer video has been started", mConfig.id);
        }
    }

    if ((flag & STREAMER_FLAG_STREAMING_AUDIO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        if (!mMediaCapture->isAudioCapturing()) {
            Statistics::Instance().stop(mConfig.id, DECODER_AUDIO);
            Statistics::Instance().setMaxFrame(mConfig.id, DECODER_AUDIO, AUDIO_BUFFER_SIZE);
            //Statistics::Instance().setNetDevice(mConfig.id, DECODER_AUDIO, mStreamConnector->getAudConnector());
            mAudIndex = 0;

            if (mConfig.captureConfig.audio.codec != AUDIO_CODEC_PCM) {
                mMediaCapture->enableAudioEncoder(true);
            } else {
                mMediaCapture->enableAudioEncoder((mFlag & STREAMER_FLAG_RECORD_MEDIA) != 0);
            }

            mMediaCapture->startAudio();
            LOGI("[%d]streamer audio started", mConfig.id);
        } else {
            LOGI("[%d]streamer audio has been started", mConfig.id);
        }
    }
}

void Streamer::stop(StreamerFlag flag) {
    std::lock_guard<std::mutex> lock(mMutex);
    LOGI("[%d]streamer stop", mConfig.id);

    if ((flag & STREAMER_FLAG_STREAMING_VIDEO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        if (mMediaCapture->isVideoCapturing()) {
            mMediaCapture->stopVideo();
            Statistics::Instance().stop(mConfig.id, DECODER_VIDEO);
            LOGI("[%d]streamer video stopped", mConfig.id);
        } else {
            LOGI("[%d]streamer video has been stopped", mConfig.id);
        }
    }

    if ((flag & STREAMER_FLAG_STREAMING_AUDIO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        if (mMediaCapture->isAudioCapturing()) {
            mMediaCapture->stopAudio();
            Statistics::Instance().stop(mConfig.id, DECODER_AUDIO);
            LOGI("[%d]streamer audio stopped", mConfig.id);
        } else {
            LOGI("[%d]streamer audio has been stopped", mConfig.id);
        }
    }

    /*if (mStreamConnector->isRunning() && !mMediaCapture->isVideoCapturing()) {
        LOG("[%d]stop stream connector", mConfig.id);
        mConnected = false;
        mStreamConnector->stop();
    }*/
}

bool Streamer::isRunning() const {
    std::lock_guard<std::mutex> lock(mMutex);
    return mMediaCapture->isVideoCapturing() || mMediaCapture->isAudioCapturing();
}

bool Streamer::isConnected() const {
    return false;//mStreamConnector->isConnected();
}

void Streamer::startRecording() {
    mRecorder->start();
}

void Streamer::stopRecording() {
    mRecorder->stop();
}

bool Streamer::isRecording() const {
    return mRecorder->isRunning();
}

void Streamer::setFlag(int flag) {
    mFlag = flag;
}

bool Streamer::hasFlag(int flag) const {
    return (mFlag & flag) != 0;
}

void Streamer::setCallback(Streamer::Callback *callback) {
    mCallback = callback;
}

void Streamer::onVideoBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    int size = bufferInfo->size;
    static int count = 0;
    count++;
    if (count > 300) {
        LOGI("[%d]on video buffer available, size:%d", mConfig.id, size);
        count = 0;
    }

    if (mFlag >= STREAMER_FLAG_RECORD_MEDIA) {
        mRecorder->videoBufferAvailable(buffer, bufferInfo);
    }

    if (bufferInfo->flags & AMEDIACODEC_BUFFER_FLAG_CODEC_CONFIG) {
        if (Statistics::Instance().enablePrintf(255)) {
            LOGE("[%d]vid encout config:%d, flag: %d, size: %d", mConfig.id, mVidIndex, bufferInfo->flags, size);
        }
        return;
    }

    auto current = getCurrentTimestampUs();
    Statistics::Instance().encFrameOut(mConfig.id, DECODER_VIDEO, mVidIndex, bufferInfo->presentationTimeUs, size);

    if (false/*mStreamConnector->isVideoConnected()*/) {
        Statistics::Instance().sendFrameIn(mConfig.id, DECODER_VIDEO, mVidIndex, current);
        YvrnetStatus yvrnetStatus = {0};
        Statistics::Instance().getYvrnetStatus(mConfig.id, DECODER_VIDEO, mVidIndex, &yvrnetStatus);
        /*mStreamConnector->sendVideoData(reinterpret_cast<const char *>(buffer),
                                        size,
                                        current,
                                        mConfig.captureConfig.video.codec,
                                        reinterpret_cast<const char *>(&yvrnetStatus),
                                        sizeof(YvrnetStatus));*/
        Statistics::Instance().sendFramePro(mConfig.id, DECODER_VIDEO, mVidIndex, current);
    }

    mVidIndex++;
}

void Streamer::onVideoOutputFormatChanged(AMediaFormat *mediaFormat) {
    int width, height;
    AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_WIDTH, &width);
    AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_HEIGHT, &height);
    LOGI("[%d]on streamer video size changed, w:%d h:%d", mConfig.id, width, height);

    if (mFlag >= STREAMER_FLAG_RECORD_MEDIA) {
        mRecorder->videoOutputFormatChanged(mediaFormat);
    }
}

//onAudioBufferAvailable
void Streamer::onAudioBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    int size = bufferInfo->size;
    static int count = 0;
    count++;
    if (count > 300) {
        LOGI("[%d]on audio buffer available, size:%d", mConfig.id, size);
        count = 0;
    }

    if (mFlag >= STREAMER_FLAG_RECORD_MEDIA) {
        mRecorder->audioBufferAvailable(buffer, bufferInfo);
    }

    if (bufferInfo->flags & AMEDIACODEC_BUFFER_FLAG_CODEC_CONFIG) {
        if (Statistics::Instance().enablePrintf(10)) {
            LOGI("[%d]aud enc config size:%d", mConfig.id, bufferInfo->size);
            for (int i = 0; i < bufferInfo->size; ++i) {
                LOGI("[%d]code data %d-0x%x", mConfig.id, i, buffer[i]);
            }
        }
        return;
    }

    auto current = getCurrentTimestampUs();
    Statistics::Instance().encFrameOut(mConfig.id, DECODER_AUDIO, mAudIndex,
                                       bufferInfo->presentationTimeUs, size);

    if (true/*mStreamConnector->isAudioConnected()*/) {
        Statistics::Instance().sendFrameIn(mConfig.id, DECODER_AUDIO, mAudIndex, current);
        YvrnetStatus yvrnetStatus = {0};
        Statistics::Instance().getYvrnetStatus(mConfig.id, DECODER_AUDIO, mAudIndex, &yvrnetStatus);
        yvrnetStatus.reserved1 = mConfig.captureConfig.audio.channelCount;
        yvrnetStatus.reserved2 = mConfig.captureConfig.audio.sampleRate;
        //sendAudioData
        /*mStreamConnector->sendAudioData(reinterpret_cast<const char *>(buffer),
                                        size,
                                        current,
                                        mConfig.captureConfig.audio.codec,
                                        reinterpret_cast<const char *>(&yvrnetStatus),
                                        sizeof(YvrnetStatus));*/
        Statistics::Instance().sendFramePro(mConfig.id, DECODER_AUDIO, mAudIndex, current);
    }

    mAudIndex++;
}

void Streamer::onAudioOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mFlag >= STREAMER_FLAG_RECORD_MEDIA) {
        mRecorder->audioOutputFormatChanged(mediaFormat);
    }
}

void Streamer::onVideoConnectionChanged(bool connected) {
    LOGI("[%d]on video connection changed %d", mConfig.id, connected);
    Statistics::Instance().netConnect(mConfig.id, DECODER_VIDEO, connected);
    if (!connected) {
        Statistics::Instance().stop(mConfig.id, DECODER_VIDEO);
    }

    bool conn = Streamer::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}

void Streamer::onVideoReceived(const char *data, int size) {
    if (size == sizeof(YvrnetStatus) && data) {
        YvrnetStatus *pStatic = (YvrnetStatus *) data;

        if (pStatic->u64InterTime != 0) {
            Statistics::Instance().decFrame(mConfig.id, DECODER_VIDEO, pStatic->sendID, pStatic->u64DecTime);
            Statistics::Instance().playFrame(mConfig.id, DECODER_VIDEO, pStatic->sendID, pStatic->u64InterTime);
            Statistics::Instance().totalFrameOut(mConfig.id, DECODER_VIDEO, pStatic->sendID,pStatic->u64FrameBeginTime);
        } else{
            Statistics::Instance().sendFrameOut(mConfig.id, DECODER_VIDEO, pStatic->sendID, pStatic->u64SendTime);
        }
    }
}

void Streamer::onVideoRequestKeyframe(bool enable) {
    LOGI("[%d]on video request keyframe: %d", mConfig.id, enable);
    if (enable) {
        mMediaCapture->requestKeyframe();
    }
}

//onAudioConnectionChanged
void Streamer::onAudioConnectionChanged(bool connected) {
    LOGI("[%d]on audio connection changed %d", mConfig.id, connected);
    Statistics::Instance().netConnect(mConfig.id, DECODER_AUDIO, connected);
    if (!connected) {
        Statistics::Instance().stop(mConfig.id, DECODER_AUDIO);
    }

    bool conn = Streamer::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}

//onAudioReceived
void Streamer::onAudioReceived(const char *data, int size) {
    if (size == sizeof(YvrnetStatus) && data) {
        YvrnetStatus *pStatic = (YvrnetStatus *) data;

        if (pStatic->u64InterTime != 0) {
            Statistics::Instance().decFrame(mConfig.id, DECODER_AUDIO, pStatic->sendID, pStatic->u64DecTime);
            Statistics::Instance().playFrame(mConfig.id, DECODER_AUDIO, pStatic->sendID, pStatic->u64InterTime);
            Statistics::Instance().totalFrameOut(mConfig.id, DECODER_AUDIO, pStatic->sendID,pStatic->u64FrameBeginTime);
        } else{
            Statistics::Instance().sendFrameOut(mConfig.id, DECODER_AUDIO, pStatic->sendID, pStatic->u64SendTime);
        }
    }
}

//onAudioRequestKeyframe
void Streamer::onAudioRequestKeyframe(bool enable) {
}

Streamer::Recorder::Recorder(Streamer *streamer) {
    mStreamer = streamer;
    mFD = -1;
    mAudioTrackIndex = -1;
    mVideoTrackIndex = -1;
    mMediaMuxer = nullptr;
    mRunning = false;
}

Streamer::Recorder::~Recorder() = default;

void Streamer::Recorder::start() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mRunning) {
        return;
    }
    mRunning = true;
    LOGI("[%d]start record, flag:%d", mStreamer->mConfig.id, mStreamer->mFlag);
    resetMuxerTrack();

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_AUDIO)) {
        mAudioFile = "sdcard/" + std::to_string(mStreamer->mConfig.id) + "audio.stream";
        mAudioStream.open(mAudioFile.c_str(), std::ios::out | std::ios::binary);
        if (!mAudioStream.is_open()) {
            LOGE("[%d]open audio.stream failed, error %s", mStreamer->mConfig.id,
                 std::strerror(errno));
        }
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_VIDEO)) {
        mVideoFile = "sdcard/" + std::to_string(mStreamer->mConfig.id) + "video.stream";
        mVideoStream.open(mVideoFile.c_str(), std::ios::out | std::ios::binary);
        if (!mVideoStream.is_open()) {
            LOGE("[%d]open video.stream failed, error %s", mStreamer->mConfig.id,
                 std::strerror(errno));
        }
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        mMediaFile = "sdcard/" + std::to_string(mStreamer->mConfig.id) + "media.mp4";
        mFD = open(mMediaFile.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (mFD < 0) {
            LOGE("[%d]open file descriptor failed", mStreamer->mConfig.id);
            return;
        }

        mMediaMuxer = AMediaMuxer_new(mFD, AMEDIAMUXER_OUTPUT_FORMAT_MPEG_4);
    }
}

void Streamer::Recorder::stop() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        return;
    }
    mRunning = false;

    LOGI("[%d]stop record", mStreamer->mConfig.id);
    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_AUDIO)) {
        mAudioStream.close();
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_VIDEO)) {
        mVideoStream.close();
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        if (mMediaMuxer != nullptr) {
            AMediaMuxer_stop(mMediaMuxer);
            AMediaMuxer_delete(mMediaMuxer);
        }

        if (close(mFD) < 0) {
            LOGE("[%d]close fd %d failed", mStreamer->mConfig.id, mFD);
        }
    }
}

bool Streamer::Recorder::isRunning() const {
    std::lock_guard<std::mutex> lock(mMutex);
    return mRunning;
}

void Streamer::Recorder::videoBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        return;
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_VIDEO)) {
        mVideoStream.write(reinterpret_cast<const char *>(buffer), bufferInfo->size);
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        if (isMuxerReady()) {
            auto now = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch());
            long microseconds = static_cast<long>(duration.count());
            bufferInfo->presentationTimeUs = microseconds;
            AMediaMuxer_writeSampleData(mMediaMuxer, mVideoTrackIndex, buffer, bufferInfo);
        }
    }
}

void Streamer::Recorder::videoOutputFormatChanged(AMediaFormat *mediaFormat) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        return;
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        mVideoTrackIndex = AMediaMuxer_addTrack(mMediaMuxer, mediaFormat);
        LOGI("[%d]on video output format changed, index:%ld", mStreamer->mConfig.id, mVideoTrackIndex.load());

        if (mVideoTrackIndex >= 0 && mAudioTrackIndex >= 0) {
            media_status_t status = AMediaMuxer_start(mMediaMuxer);
            if (status != AMEDIA_OK) {
                LOGE("video start media muxer error:%d", status);
            }
        }
    }
}

void Streamer::Recorder::audioBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        return;
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_AUDIO)) {
        mAudioStream.write(reinterpret_cast<const char *>(buffer), bufferInfo->size);
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        if (isMuxerReady()) {
            auto now = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch());
            long microseconds = static_cast<long>(duration.count());
            bufferInfo->presentationTimeUs = microseconds;
            AMediaMuxer_writeSampleData(mMediaMuxer, mAudioTrackIndex, buffer, bufferInfo);
        }
    }
}

void Streamer::Recorder::audioOutputFormatChanged(AMediaFormat *mediaFormat) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        return;
    }

    if (mStreamer->hasFlag(STREAMER_FLAG_RECORD_MEDIA)) {
        mAudioTrackIndex = AMediaMuxer_addTrack(mMediaMuxer, mediaFormat);
        LOGI("[%d]on audio output format changed, index:%ld", mStreamer->mConfig.id, mAudioTrackIndex.load());

        if (mVideoTrackIndex >= 0 && mAudioTrackIndex >= 0) {
            media_status_t status = AMediaMuxer_start(mMediaMuxer);
            if (status != AMEDIA_OK) {
                LOGE("audio start media muxer error:%d", status);
            }
        }
    }
}

void Streamer::Recorder::setAudioOutputFile(std::string file) {
    mAudioFile = std::move(file);
}

void Streamer::Recorder::setVideoOutputFile(std::string file) {
    mVideoFile = std::move(file);
}

void Streamer::Recorder::setMediaOutputFile(std::string file) {
    mMediaFile = std::move(file);
}

bool Streamer::Recorder::isMuxerReady() const {
    return mVideoTrackIndex >= 0 && mAudioTrackIndex >= 0;
}

void Streamer::Recorder::resetMuxerTrack() {
    mAudioTrackIndex = -1;
    mVideoTrackIndex = -1;
}
