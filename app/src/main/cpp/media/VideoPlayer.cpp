#include <unistd.h>
#include "VideoPlayer.h"
#include "VideoDecoder.h"
#include "Common.h"
#include "utils/Statistics.hpp"
#include <sys/resource.h>

//#define DEBUG_VIDEO

static const std::byte NAL_TYPE_P = static_cast<const std::byte>(1);
static const std::byte NAL_TYPE_IDR = static_cast<const std::byte>(5);
static const std::byte NAL_TYPE_SPS = static_cast<const std::byte>(7);
static const std::byte H265_NAL_TYPE_VPS = static_cast<const std::byte>(32);
static const std::byte H265_NAL_TYPE_IDR_W_RADL = static_cast<const std::byte>(19);


VideoPlayer::VideoPlayer() {
    mSurfaceState = SURFACE_STATE_IDLE;
    mSurfaceTexture = nullptr;
}

VideoPlayer::~VideoPlayer() = default;

void VideoPlayer::start() {
    LOGI("[%d]video player start", mId);
    mVideoPlayerQueue.clear();
    mRenderQueue.clear();
    mSurfaceState = SURFACE_STATE_AVAILABLE;
#ifdef ASYNC_DECODE
    mInputQueue.clear();
#endif

    Statistics::Instance().stop(mId, DECODER_VIDEO);
    Statistics::Instance().setMaxFrame(mId, DECODER_VIDEO, VIDEO_BUFFER_SIZE);
    Statistics::Instance().setNetDevice(mId, DECODER_VIDEO, mConnector.get());

    Player::start();

#ifdef DEBUG_VIDEO
    mOfstream.open("/data/user/0/com.pfdm.screencastsink/files/video.stream", std::ios::out | std::ios::binary);
    if (!mOfstream.is_open()) {
        LOGE("open video.stream failed, error %s", std::strerror(errno));
    }
#endif

    LOGI("[%d]video player started", mId);
}

void VideoPlayer::stop() {
    LOGI("[%d]video player stop", mId);
    Player::stop();
    Statistics::Instance().stop(mId, DECODER_VIDEO);

#ifdef DEBUG_VIDEO
    mOfstream.close();
#endif

    LOGI("[%d]video player stopped", mId);
}

void VideoPlayer::configure(Player::Config &config) {
    Player::configure(config);
    Player::setAddress(config.video.ip, config.video.port);

    VideoDecoder::setId(config.id);
    VideoDecoder::setMimeType(config.video.mimeType);
    VideoDecoder::setSize(config.video.width, config.video.height);

    LOGI("[%d]video configure:%s", config.id, config.video.toString().c_str());
}

void VideoPlayer::setFrameDropThreshold(int threshold) {
    mConfig.video.dropThreshold = threshold;
    LOGI("[%d]set video frame drop threshold value:%d", mId, threshold);
}

void VideoPlayer::setSurfaceTexture(ASurfaceTexture *surfaceTexture) {
    mSurfaceTexture = surfaceTexture;
}

void VideoPlayer::onReceived(const char *data, int size) {
    if (size == sizeof(YvrnetStatus) && data) {
        YvrnetStatus *pStatic = (YvrnetStatus *) data;

        if (pStatic->u64InterTime != 0) {
            Statistics::Instance().sendFrame(mId, DECODER_VIDEO, pStatic->sendID, pStatic->u64SendTime);
            Statistics::Instance().totalFrame(mId, DECODER_VIDEO, pStatic->sendID,pStatic->u64FrameBeginTime);
        }
    }
}

void VideoPlayer::onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) {
    static int count = 0;
    count++;
    if (count > 300) {
        LOGI("[%d]on video data received, size:%d", mId, size);
        count = 0;
    }

    YvrnetStatus *pRevStatus = (YvrnetStatus *) pStatus;
    Statistics::Instance().totalFrameIn(mId, DECODER_VIDEO,pRevStatus->sendID,pRevStatus->u64FrameBeginTime);
    Statistics::Instance().capFrame(mId, DECODER_VIDEO,pRevStatus->sendID,pRevStatus->u64CapTime);
    Statistics::Instance().sendFrameIn(mId, DECODER_VIDEO,pRevStatus->sendID,pRevStatus->u64SendTime);
    Statistics::Instance().encFrame(mId, DECODER_VIDEO,pRevStatus->sendID,pRevStatus->u64EncTime, size);

    const std::byte *frameBuffer;
    frameBuffer = reinterpret_cast<const std::byte *>(data);

    std::byte NALType;
    if (mediaType == VIDEO_CODEC_H264)
        NALType = frameBuffer[4] & std::byte(0x1F);
    else
        NALType = (frameBuffer[4] >> 1) & std::byte(0x3F);

    if ((mediaType == VIDEO_CODEC_H264 && NALType == NAL_TYPE_SPS) ||
        (mediaType == VIDEO_CODEC_H265 && NALType == H265_NAL_TYPE_VPS)) {
        // This frame contains (VPS + )SPS + PPS + IDR on NVENC H.264 (H.265) stream.
        // (VPS + )SPS + PPS has short size (8bytes + 28bytes in some environment),
        // so we can assume SPS + PPS is contained in first fragment.
        if (!mVideoPlayerQueue.push((uint8_t *) data, size, (uint32_t) NAL_TYPE_IDR, pRevStatus->sendID)) {
            LOGW("[%d]push %d video i data full", mId, pRevStatus->sendID);
        }
    } else {
        if (!mVideoPlayerQueue.push((uint8_t *) data, size, (uint32_t) NAL_TYPE_P, pRevStatus->sendID)) {
            LOGW("[%d]push %d video p data full", mId, pRevStatus->sendID);
        }
    }

    if (mConfig.trace > 0) {
        LOGI("trace:queue size %ld", mVideoPlayerQueue.length());
    }
}

void VideoPlayer::onConnectionChanged(bool connected) {
    Player::onConnectionChanged(connected);
    Statistics::Instance().netConnect(mId, DECODER_VIDEO, connected);
    if (!connected) {
        Statistics::Instance().stop(mId, DECODER_VIDEO);
    }
}

void VideoPlayer::onStart() {
    Player::onStart();
    LOGI("[%d]video player on start", mId);

    std::string threadName = "video-player" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for video player thread:%d rc:%d", mId, self, rc);

    while (isRunning()) {
        VideoPlayFrame *videoData = mVideoPlayerQueue.peek();
        if (videoData == nullptr) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        if (videoData->type == (int32_t) NAL_TYPE_IDR) {
            LOGI("[%d]got nal type sps", mId);
            VideoDecoder::setId(mId);
            VideoDecoder::prepare();
            VideoDecoder::setBuffer(AMEDIAFORMAT_KEY_CSD_0, videoData->buffer, videoData->size);
            VideoDecoder::start();
            return;
        } else {
            mVideoPlayerQueue.pop();
        }
    }
}

void VideoPlayer::onStop() {
    Player::onStop();
    LOGI("[%d]video player on stop", mId);

    Decoder::stop();
}

void VideoPlayer::onFrameAvailable() {
    std::lock_guard<std::mutex> lock(mMutex);

    if (mSurfaceState != SURFACE_STATE_RENDERING) {
        return;
    }

    mSurfaceState = SURFACE_STATE_AVAILABLE;
}

void VideoPlayer::clearAvailable() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mSurfaceState != SURFACE_STATE_AVAILABLE) {
        return;
    }

    if (!isRunning()) return;
    if (mSurfaceTexture == nullptr) return;

    mSurfaceState = SURFACE_STATE_IDLE;
    ASurfaceTexture_updateTexImage(mSurfaceTexture);

    releaseOutputBuffer();
}

void VideoPlayer::releaseOutputBuffer() {
    long index;
    if (!mFocused) {
        if (!mRenderQueue.tryPop(index)) {
            return;
        }

        //LOGW("[%d]discard frame, focus:%d", mId, mFocused);
        VideoDecoder::releaseOutputBuffer(index, false);
        Statistics::Instance().playFrameOut(mId, DECODER_VIDEO, true);
        return;
    }

    if (mSurfaceState != SURFACE_STATE_IDLE) {
        //LOGW("Conflict with current rendering frame. Defer processing.");
        return;
    }

    if (!mRenderQueue.tryPop(index)) {
        //LOGW("[%d]try pop video index error", mId);
        return;
    }

    mSurfaceState = SURFACE_STATE_RENDERING;
    VideoDecoder::releaseOutputBuffer(index, true);
    Statistics::Instance().playFrameOut(mId, DECODER_VIDEO, false);
}

void VideoPlayer::render() {
    Player::render();

    if (!isRunning()) {
        return;
    }

    unsigned long length = mRenderQueue.size();
    if (length == 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

    long index;
    while (length > mConfig.video.dropThreshold) {
        if (!mRenderQueue.tryPop(index)) {
            LOGW("[%d]try pop video index error, break", mId);
            break;
        }

        LOGW("[%d]discard old video frame, size:%ld", mId, length);
        VideoDecoder::releaseOutputBuffer(index, false);
        Statistics::Instance().playFrameOut(mId, DECODER_VIDEO, true);
        length--;
    }

    {
        std::lock_guard<std::mutex> lock(mMutex);
        releaseOutputBuffer();
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
}

void VideoPlayer::decode() {
    Decoder::decode();

    if (!isRunning()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

#ifdef ASYNC_DECODE
    long index;
    VideoFrame *videoData = mVideoPlayerQueue.peek();
    if (videoData == nullptr) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

    if (!mInputQueue.tryPop(index)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

    videoData = mVideoPlayerQueue.pop();
    long microseconds = getCurrentTimestampUs();
    bool bRet = VideoDecoder::inputBuffer(index, videoData->buffer, videoData->size, microseconds);
    if (bRet) {
        Statistics::Instance().decFrameIn(mId, DECODER_VIDEO, videoData->frameId, microseconds);
    }

#else

    long index;
    do {
        VideoPlayFrame *videoData = mVideoPlayerQueue.peek();
        if (videoData == nullptr) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            break;
        }

#ifdef DEBUG_VIDEO
        mOfstream.write(reinterpret_cast<const char *>(videoData->buffer), videoData->size);
#endif

        index = VideoDecoder::dequeueInputBuffer(100);
        if (index >= 0) {
            videoData = mVideoPlayerQueue.pop();

            long microseconds = getCurrentTimestampUs();
            bool bRet = VideoDecoder::inputBuffer(index, videoData->buffer, videoData->size, microseconds);
            if (bRet) {
                Statistics::Instance().decFrameIn(mId, DECODER_VIDEO, videoData->frameId, microseconds);
            }
        }
    } while (index >= 0);

    do {
        AMediaCodecBufferInfo bufferInfo;
        index = VideoDecoder::dequeueOutputBuffer(&bufferInfo);
        if (index >= 0) {
            int frameId = Statistics::Instance().decFrameOut(mId, DECODER_VIDEO, STATIS_INVALID_ID, bufferInfo.presentationTimeUs);
            Statistics::Instance().playFrameIn(mId, DECODER_VIDEO, frameId, getCurrentTimestampUs());

            if (!mRenderQueue.tryPush(index)) {
                LOGW("try push video index failed, release index %ld", index);
                VideoDecoder::releaseOutputBuffer(index, false);
                Statistics::Instance().playFrameOut(mId, DECODER_VIDEO, true);
            }
        } else if (index == AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED) {
            LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED", mId);
        } else if (index == AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED) {
            LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED", mId);
            if (Player::mCallback != nullptr) {
                auto format = AMediaCodec_getOutputFormat(mMediaCodec);
                Player::mCallback->onOutputFormatChanged(format);
            }
        } else if (index == AMEDIACODEC_INFO_TRY_AGAIN_LATER) {
            //LOGW("AMEDIACODEC_INFO_TRY_AGAIN_LATER");
        }
    } while (index >= 0);

#endif
}

void VideoPlayer::onInputAvailable(AMediaCodec *codec, int32_t index) {
    Decoder::onInputAvailable(codec, index);
#ifdef ASYNC_DECODE
    if (!mInputQueue.tryPush(index)) {
        LOGW("[%d]try push input queue error", mId);
    }
#endif
}

void VideoPlayer::onOutputAvailable(AMediaCodec *codec, int32_t index, AMediaCodecBufferInfo *bufferInfo) {
    Decoder::onOutputAvailable(codec, index, bufferInfo);
    if (!mRenderQueue.tryPush(index)) {
        LOGW("try push render queue error, release index %d", index);
        VideoDecoder::releaseOutputBuffer(index, false);
        Statistics::Instance().playFrameOut(mId, DECODER_VIDEO, true);
        return;
    }

    int frameId = Statistics::Instance().decFrameOut(mId, DECODER_VIDEO, STATIS_INVALID_ID,
                                                     bufferInfo->presentationTimeUs);
    Statistics::Instance().playFrameIn(mId, DECODER_VIDEO, frameId, getCurrentTimestampUs());
}

void VideoPlayer::onFormatChanged(AMediaCodec *codec, AMediaFormat *format) {
    Decoder::onFormatChanged(codec, format);
    LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED, format:%s", mId, AMediaFormat_toString(format));
    if (Player::mCallback != nullptr) {
        Player::mCallback->onOutputFormatChanged(format);
    }
}

void VideoPlayer::onError(AMediaCodec *codec, media_status_t error, int32_t actionCode, const char *detail) {
    Decoder::onError(codec, error, actionCode, detail);
}

void VideoPlayer::onRequestKeyframe(bool enable) {

}
