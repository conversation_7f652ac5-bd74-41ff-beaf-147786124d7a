#ifndef SCREENCASTSOURCE_ENCODER_H
#define SCREENCASTSOURCE_ENCODER_H

#include "media/NdkMediaCodec.h"
#include "media/NdkMediaFormat.h"
#include <string>
#include <thread>

#define COLOR_FORMAT_SURFACE            (0x7f000789)
#define COLOR_FORMAT_YUV420_FLEXIBLE    (0x7f420888)
#define COLOR_FORMAT_YUV420_SEMIPLANAR  (0x00000015)

#define MIME_TYPE_VIDEO_AVC "video/avc"
#define MIME_TYPE_VIDEO_HEVC "video/hevc"

#define MIME_TYPE_AUDIO_MP4A_LATM "audio/mp4a-latm"
#define MIME_TYPE_AUDIO_OPUS "audio/opus"
#define MIME_TYPE_AUDIO_FLAC "audio/flac"
#define MIME_TYPE_AUDIO_PCM "audio/pcm"

class Encoder {
public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void
        onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) = 0;

        virtual void onOutputFormatChanged(AMediaFormat *mediaFormat) = 0;
    };

public:
    Encoder();

    virtual ~Encoder();

    virtual void prepare();

    virtual void onPrepared();

    virtual void start();

    virtual void stop();

    virtual void onReleased();

    virtual long dequeueInputBuffer();

    virtual long dequeueInputBuffer(int64_t timeoutUs);

    virtual int inputBuffer(long index, uint8_t *buffer, uint32_t size, uint64_t timestamp);

    virtual long dequeueOutputBuffer(AMediaCodecBufferInfo *info);

    virtual uint8_t *outputBuffer(long index, size_t *out_size);

    virtual void releaseOutputBuffer(int index);

    virtual void process();

    virtual void run();

    virtual void onEncoderStart();

    virtual void onEncoderStop();

    virtual void setCallback(Callback *callback);

    void setMimeType(std::string type);

    void setId(int id);

protected:
    std::string mMimeType;
    AMediaCodec *mMediaCodec;
    AMediaFormat *mMediaFormat;
    Callback *mCallback;
    std::thread mThread;
    bool mRunning;
    int mId;
};


#endif //SCREENCASTSOURCE_ENCODER_H
