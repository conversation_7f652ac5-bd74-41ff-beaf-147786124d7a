#include "AudioEncoder.h"
#include "Common.h"
#include <sys/resource.h>

AudioEncoder::AudioEncoder() {
    mBitRate = 0;
    mChannelCount = 0;
    mSampleRate = 0;
}

AudioEncoder::~AudioEncoder() = default;

void AudioEncoder::prepare() {
    Encoder::prepare();

    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_BIT_RATE, mBitRate);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_CHANNEL_COUNT, mChannelCount);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_SAMPLE_RATE, mSampleRate);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_PCM_ENCODING, 2);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_AAC_PROFILE, 39);
    AMediaFormat_setInt32(mMediaFormat, "vendor.qti-ext-enc-low-latency.enable", 1);
//    AMediaFormat_setInt32(mMediaFormat, "prepend-sps-pps-to-idr-frames", 1);
}

void AudioEncoder::onPrepared() {
    Encoder::onPrepared();
}
void AudioEncoder::onEncoderStart() {
    Encoder::onEncoderStart();

    std::string threadName = "audio-encoder" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for audio encoder thread:%d rc:%d", mId, self, rc);
}

void AudioEncoder::onEncoderStop() {
    Encoder::onEncoderStop();
}

void AudioEncoder::setBitRate(int bitRate) {
    mBitRate = bitRate;
}

void AudioEncoder::setChannelCount(int channelCount) {
    mChannelCount = channelCount;
}

void AudioEncoder::setSampleRate(int sampleRate) {
    mSampleRate = sampleRate;
}
