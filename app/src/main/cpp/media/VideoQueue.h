#ifndef SCREENCASTSOURCE_VIDEOQUEUE_H
#define SCREENCASTSOURCE_VIDEOQUEUE_H

#include <memory>
#include <mutex>
#include "Common.h"

//sink
//#define VIDEO_BUFFER_SIZE (6 * 1024 * 1024)
//#define VIDEO_QUEUE_MAX_SIZE 40
#define VIDEO_BUFFER_SIZE (20 * 1024 * 1024)
#define VIDEO_QUEUE_MAX_SIZE 20


typedef struct {
    //uint8_t buffer[VIDEO_BUFFER_SIZE];
    void *buffer;
    uint32_t size;
    uint64_t timestamp;
} VideoFrame;

typedef struct {
    VideoFrame frames[VIDEO_QUEUE_MAX_SIZE];
    long front;
    long rear;
    long size;
} VideoFrameQueue;

class VideoQueue {
public:
    explicit VideoQueue(bool lock = true);
    ~VideoQueue();
    bool push(void *buffer, uint32_t size, uint64_t timestamp);
    bool push(VideoFrame *frame);
    bool pop(VideoFrame *frame);
    VideoFrame *peek();
    VideoFrame *pop();
    bool isEmpty();
    bool isFull();
    long length();
    void clear();

private:
    bool pushInternal(void *buffer, uint32_t size, uint64_t timestamp);
    bool pushInternal(VideoFrame *frame);
    bool popInternal(VideoFrame *frame);
    VideoFrame *peekInternal();
    VideoFrame *popInternal();
    bool isEmptyInternal();
    bool isFullInternal();
    long lengthInternal();

private:
    std::shared_ptr <VideoFrameQueue> mQueue;
    std::mutex mMutex;
    bool mLock;
};

#endif //SCREENCASTSOURCE_VIDEOQUEUE_H
