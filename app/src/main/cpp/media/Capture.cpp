#include "Capture.h"
#include "Common.h"

std::string Capture::Config::Video::toString() const {
    return "width:" + std::to_string(width)
           + " height:" + std::to_string(height)
           + " bitRate:" + std::to_string(bitRate)
           + " frameRate:" + std::to_string(frameRate)
           + " colorFormat:" + std::to_string(colorFormat)
           + " iFrameInterval:" + std::to_string(iFrameInterval)
           + " intraRefreshPeriod:" + std::to_string(intraRefreshPeriod)
           + " mimeType:" + mimeType;
}

std::string Capture::Config::Audio::toString() const {
    return "bitRate:" + std::to_string(bitRate)
           + " channelCount:" + std::to_string(channelCount)
           + " sampleRate:" + std::to_string(sampleRate)
           + " mimeType:" + mimeType;
}

std::string Capture::Config::toString() const {
    return "device id:" + std::to_string(deviceId)
           + " video{" + video.toString() + "}"
           + " audio{" + audio.toString() + "}";
}

Capture::Capture() {
    mRunning = false;
    mCallback = nullptr;
}

Capture::~Capture() = default;

void Capture::start() {
    mRunning = true;

    mThread = std::thread(&Capture::run, this);
}

void Capture::stop() {
    mRunning = false;

    if (mThread.joinable()) {
        mThread.join();
    }
}

void Capture::onStart() {
}

void Capture::onStop() {
}

void Capture::loop() {
}

void Capture::configure(Config &config) {
    mConfig = config;
}

void Capture::run() {
    onStart();

    while (mRunning) {
        loop();
    }

    onStop();
}

bool Capture::isRunning() {
    return mRunning;
}

void Capture::setCallback(Callback *callback) {
    mCallback = callback;
}
