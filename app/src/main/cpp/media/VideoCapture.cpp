#include "VideoCapture.h"
#include "Common.h"
#include "CameraManager.h"
#include "utils/Statistics.hpp"
#include <sys/resource.h>

VideoCapture::VideoCapture() {
    mGraphicsRender = nullptr;
    mEGLContext = nullptr;
    mCodecConfig = nullptr;
    mVideoCapIndex = 0;
    mCodecConfigSize = 0;
    mFrameDropCount = 0;
}

VideoCapture::~VideoCapture() = default;

void VideoCapture::configure(Config &config) {
    Capture::configure(config);

    VideoEncoder::setId(config.deviceId);
    VideoEncoder::setMimeType(config.video.mimeType);
    VideoEncoder::setSize(config.video.width, config.video.height);
    VideoEncoder::setBitRate(config.video.bitRate);
    VideoEncoder::setFrameRate(config.video.frameRate);
    VideoEncoder::setIFrameInterval(config.video.iFrameInterval);
    VideoEncoder::setIntraRefreshPeriod(config.video.intraRefreshPeriod);
    VideoEncoder::setCallback(this);

    LOGI("[%d]video capture config, %s", config.deviceId, config.video.toString().c_str());
}

void VideoCapture::start() {
    LOGI("[%d]video capture start", mConfig.deviceId);
    mVideoQueue.clear();
    mVideoCapIndex = 0;
    mFrameDropCount = 0;
    VideoEncoder::start();
    Capture::start();

    SetCameraResolve(static_cast<android::yvr::DPDeviceType>(mConfig.deviceId), mConfig.video.width,
                     mConfig.video.height);
    //StartCameraDevice
    StartCameraDevice(static_cast<android::yvr::DPDeviceType>(mConfig.deviceId), this);
    LOGI("[%d]video capture started", mConfig.deviceId);
}

void VideoCapture::stop() {
    LOGI("[%d]video capture stop", mConfig.deviceId);
    StopCameraDevice(static_cast<android::yvr::DPDeviceType>(mConfig.deviceId));

    Capture::stop();
    VideoEncoder::stop();

    LOGI("[%d]video capture stopped", mConfig.deviceId);
}

void VideoCapture::requestKeyframe() {
    VideoEncoder::setParameters("request-sync", 1);
}

void VideoCapture::onStart() {
    Capture::onStart();
    LOGI("[%d]video capture on start", mConfig.deviceId);

    std::string threadName = "video-capture" + std::to_string(mConfig.deviceId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for video capture thread:%d rc:%d", mConfig.deviceId, self, rc);

    mEGLContext = std::make_shared<EGLContextEnv>();
    mEGLContext->initialize(getWindow());

    mGraphicsRender = std::make_shared<GraphicsRender>(mConfig.deviceId);
    mGraphicsRender->setSize(mConfig.video.width, mConfig.video.height);
    mGraphicsRender->create();

#ifdef DEBUG_VIDEO
    std::string name = "sdcard/" + std::to_string(mConfig.deviceId) + "video.stream";
    mStream.open(name.c_str(), std::ios::out | std::ios::binary);
    if (!mStream.is_open()) {
        LOGE("open video.stream failed, error %s", std::strerror(errno));
    }
#endif
}

void VideoCapture::onStop() {
    Capture::onStop();
    LOGI("[%d]video capture on stop", mConfig.deviceId);

    mGraphicsRender->destroy();
    mGraphicsRender.reset();
    mEGLContext->clear();
    mEGLContext.reset();

    if (mCodecConfig != nullptr) {
        delete mCodecConfig;
        mCodecConfig = nullptr;
    }

#ifdef DEBUG_VIDEO
    mStream.close();
#endif
}

void VideoCapture::loop() {
    Capture::loop();

    if (!isRunning()) {
        return;
    }

    auto frame = mVideoQueue.pop();
    LOGI("[%d]VideoCapture::loop frame %p", mConfig.deviceId, frame);
    if (frame == nullptr) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

    long length = mVideoQueue.length();
    LOGI("[%d]VideoCapture::loop length %ld", mConfig.deviceId, length);
    if (length > 0) {
        LOGI("[%d]discard frame, length:%ld", mConfig.deviceId, length);
        return;
    }

    auto currentin = getCurrentTimestampUs();
    Statistics::Instance().totalFrameIn(mConfig.deviceId, DECODER_VIDEO, mVideoCapIndex, currentin);
    Statistics::Instance().capFrameIn(mConfig.deviceId, DECODER_VIDEO, mVideoCapIndex, currentin);

    if (!mGraphicsRender->setTexture((AHardwareBuffer *) frame->buffer)) {
        LOGE("[%d]camera setTexture failed", mConfig.deviceId);
        return;
    }

    mGraphicsRender->render();
    if (!mEGLContext->swapBuffers()) {
        LOGE("[%d]swap buffer failed", mConfig.deviceId);
    }else{
        auto current = getCurrentTimestampUs();
        Statistics::Instance().capFrameOut(mConfig.deviceId, DECODER_VIDEO, mVideoCapIndex, current);
        Statistics::Instance().encFrameIn(mConfig.deviceId, DECODER_VIDEO, mVideoCapIndex, current);

        mVideoCapIndex++;
    }
}

void VideoCapture::onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
#ifdef DEBUG_VIDEO
    mStream.write(reinterpret_cast<const char *>(buffer), bufferInfo->size);
#endif

    if (Capture::mCallback != nullptr) {
        if (bufferInfo->flags == AMEDIACODEC_BUFFER_FLAG_CODEC_CONFIG) {
            mCodecConfigSize = bufferInfo->size;
            mCodecConfig = new uint8_t[mCodecConfigSize];
            std::memcpy(mCodecConfig, buffer, mCodecConfigSize);
        }

        if (bufferInfo->flags == AMEDIACODEC_BUFFER_FLAG_KEY_FRAME) {
            memset(mDataBuffer, 0, DATA_BUFFER_SIZE);
            std::memcpy(mDataBuffer, mCodecConfig, mCodecConfigSize);
            std::memcpy(mDataBuffer + mCodecConfigSize, buffer, bufferInfo->size);
            bufferInfo->size += mCodecConfigSize;
            Capture::mCallback->onBufferAvailable(mDataBuffer, bufferInfo);
            return;
        }

        //MediaCapture::VideoCaptureCallback::onBufferAvailable
        Capture::mCallback->onBufferAvailable(buffer, bufferInfo);
    }
}

void VideoCapture::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (Capture::mCallback != nullptr) {
        Capture::mCallback->onOutputFormatChanged(mediaFormat);
    }
}

void VideoCapture::onFrameAvailable(CameraAHBuffer &buffer, android::yvr::DPDeviceType type) {
    if (mConfig.deviceId != type) {
        LOGE("[%d]invalid device id:%d", mConfig.deviceId, type);
        return;
    }

    static int count = 0;
    count++;

    static auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    if (duration.count() >= 1000) {
        LOGI("[%d]camera fps=%d", mConfig.deviceId, count);
        start = std::chrono::high_resolution_clock::now();
        count = 0;
    }

    if (mFrameDropCount < 120) {
        mFrameDropCount++;
        return;
    }

//    if (!mVideoQueue.push((uint8_t *) buffer.vaddr, buffer.size, 0)) {
//        LOGW("[%d]push video queue error", mConfig.deviceId);
//    }

//VideoQueue::push(uint8_t *buffer, uint32_t size, uint32_t type)
    if (!mVideoQueue.push(buffer.ahbuffer, buffer.size, 0)) {
        LOGW("[%d]push video queue error", mConfig.deviceId);
    }
}
