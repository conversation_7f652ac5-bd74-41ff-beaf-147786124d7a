#include "AudioCapture.h"
#include <tinyalsa/asoundlib.h>
#include <chrono>
#include <sys/resource.h>
#include "../utils/Statistics.hpp"


AudioCapture::AudioCapture() {
    mEncoderEnabled = false;
    mPcm = nullptr;
    mBuffer = nullptr;
    mSize = 0;
    mAudioCapIndex = 0;
}

AudioCapture::~AudioCapture() = default;

void AudioCapture::configure(Config &config) {
    Capture::configure(config);
    AudioEncoder::setId(config.deviceId);
    AudioEncoder::setMimeType(config.audio.mimeType);
    AudioEncoder::setBitRate(config.audio.bitRate);
    AudioEncoder::setChannelCount(config.audio.channelCount);
    AudioEncoder::setSampleRate(config.audio.sampleRate);
    AudioEncoder::setCallback(this);

    LOGI("[%d]audio capture config, %s", config.deviceId, config.audio.toString().c_str());
}

void AudioCapture::start() {
    LOGI("[%d]audio capture start", mConfig.deviceId);
    mixerEnabled(true);
    mAudioCapIndex = 0;

    struct pcm_config config;
    memset(&config, 0, sizeof(config));
    config.channels = mConfig.audio.channelCount;
    config.rate = mConfig.audio.sampleRate;
    config.period_size = 1024 / 8;
    config.period_count = 4;
    config.format = PCM_FORMAT_S16_LE;
    config.start_threshold = 0;
    config.stop_threshold = 0;
    config.silence_threshold = 0;

    //pcm open
    //DEVICE_ID_DP = 0,
    //DEVICE_ID_HDMI,
    mPcm = pcm_open(0, mConfig.deviceId == DEVICE_ID_DP ? 1 : 0, PCM_IN, &config);
    if (!mPcm || !pcm_is_ready(mPcm)) {
        LOGE("[%d]unable to open PCM device (%s)", mConfig.deviceId, pcm_get_error(mPcm));
        return;
    }

    mSize = pcm_frames_to_bytes(mPcm, pcm_get_buffer_size(mPcm));
    LOGI("[%d]pcm frames to bytes size %d", mConfig.deviceId, mSize);

    mBuffer = (char *) malloc(mSize);
    if (!mBuffer) {
        LOGE("[%d]unable to allocate %u bytes", mConfig.deviceId, mSize);
        free(mBuffer);
        pcm_close(mPcm);
        return;
    }

    if (mEncoderEnabled) {
        AudioEncoder::start();
    }

    Capture::start();
}

void AudioCapture::stop() {
    LOGI("[%d]audio capture stop", mConfig.deviceId);
    Capture::stop();

    if (mEncoderEnabled) {
        AudioEncoder::stop();
    }

    free(mBuffer);
    mBuffer = nullptr;
    pcm_close(mPcm);
    mPcm = nullptr;

    mixerEnabled(false);
    LOGI("[%d]audio capture stopped", mConfig.deviceId);
}

void AudioCapture::enableAudioEncoder(bool enabled) {
    mEncoderEnabled = enabled;
}

void AudioCapture::onStart() {
    Capture::onStart();
    LOGI("[%d]audio capture on start", mConfig.deviceId);

    std::string threadName = "audio-capture" + std::to_string(mConfig.deviceId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for audio capture thread:%d rc:%d", mConfig.deviceId, self, rc);

#ifdef DEBUG_AUDIO
    std::string name = "sdcard/" + std::to_string(mConfig.deviceId) + "audio.stream";
    mStream.open(name.c_str(), std::ios::out | std::ios::binary);
    if (!mStream.is_open()) {
        LOGE("open audio.stream failed, error %s", std::strerror(errno));
    }
#endif
}

void AudioCapture::onStop() {
    Capture::onStop();
    LOGI("[%d]audio capture on stop", mConfig.deviceId);

#ifdef DEBUG_AUDIO
    mStream.close();
#endif
}

void AudioCapture::loop() {
    Capture::loop();

    if (!isRunning()) {
        return;
    }

    auto currentin = getCurrentTimestampUs();
    Statistics::Instance().totalFrameIn(mConfig.deviceId, DECODER_AUDIO, mAudioCapIndex, currentin);
    Statistics::Instance().capFrameIn(mConfig.deviceId, DECODER_AUDIO, mAudioCapIndex, currentin);

    std::memset(mBuffer, 0, mSize);
    int ret = pcm_read(mPcm, mBuffer, mSize);
    if (ret) {
        LOGW("[%d]pcm read error, code:%d", mConfig.deviceId, ret);
        pcm_stop(mPcm);
        return;
    }
    auto current = getCurrentTimestampUs();
    Statistics::Instance().capFrameOut(mConfig.deviceId, DECODER_AUDIO, mAudioCapIndex, current);
    Statistics::Instance().encFrameIn(mConfig.deviceId, DECODER_AUDIO, mAudioCapIndex, current);

    mAudioCapIndex++;

#ifdef DEBUG_AUDIO
    mStream.write(reinterpret_cast<const char *>(mBuffer), mSize);
#endif

    if (mEncoderEnabled) {
        long index = dequeueInputBuffer(-1);
        if (index >= 0) {
            long microseconds = getCurrentTimestampUs();
            AudioEncoder::inputBuffer(index, (uint8_t *) mBuffer, mSize, microseconds);
        } else {
            LOGW("invalid index:%ld", index);
        }
    } else {
        if (Capture::mCallback != nullptr) {
            AMediaCodecBufferInfo bufferInfo;
            bufferInfo.size = mSize;
            bufferInfo.presentationTimeUs = getCurrentTimestampUs();
            int32_t numFrames = mSize / 4;
            //aaudio_result_t result = AAudioStream_write(mStream, mBuffer, numFrames, 10 * 1000 * 1000L);
            Capture::mCallback->onBufferAvailable((uint8_t *) mBuffer, &bufferInfo);
        }
    }
}

void AudioCapture::onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    if (Capture::mCallback != nullptr) {
        Capture::mCallback->onBufferAvailable(buffer, bufferInfo);
    }
}

void AudioCapture::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (Capture::mCallback != nullptr) {
        Capture::mCallback->onOutputFormatChanged(mediaFormat);
    }
}

void AudioCapture::mixerEnabled(bool enabled) {
    struct mixer *mixer;
    int card = 0;
    int ret = 0;

    const char *en = enabled ? "1" : "0";
    char *value1[] = {const_cast<char *>(en)};

    const std::string str = SAMPLE_RATE_MAP.at(mConfig.audio.sampleRate);
    char *value2[] = {const_cast<char *>(str.c_str())};
    const char *ch = mConfig.audio.channelCount == 1 ? "One" : "Two";
    char *value3[] = {const_cast<char *>(ch)};

    mixer = mixer_open(card);
    if (!mixer) {
        LOGE("[%d]failed to open mixer", mConfig.deviceId);
        return;
    }

    if (mConfig.deviceId == DEVICE_ID_HDMI) {
        ret = mixerSetValue(mixer, "MultiMedia1 Mixer PRI_MI2S_TX", value1, 1);
        LOGI("[%d]set MultiMedia1 Mixer PRI_MI2S_TX value:%s, ret:%d", mConfig.deviceId, value1[0], ret);
        ret = mixerSetValue(mixer, "PRIM_MI2S_TX SampleRate", value2, 1);
        LOGI("[%d]set PRIM_MI2S_TX SampleRate value:%s, ret:%d", mConfig.deviceId, value2[0], ret);
        ret = mixerSetValue(mixer, "PRIM_MI2S_TX Channels", value3, 1);
        LOGI("[%d]set PRIM_MI2S_TX Channels value:%s, ret:%d", mConfig.deviceId, value3[0], ret);
    } else {
        ret = mixerSetValue(mixer, "MultiMedia2 Mixer TERT_MI2S_TX", value1, 1);
        LOGI("[%d]set MultiMedia2 Mixer TERT_MI2S_TX value:%s, ret:%d", mConfig.deviceId, value1[0], ret);
        ret = mixerSetValue(mixer, "TERT_MI2S_TX SampleRate", value2, 1);
        LOGI("[%d]set TERT_MI2S_TX SampleRate value:%s, ret:%d", mConfig.deviceId, value2[0], ret);
        ret = mixerSetValue(mixer, "TERT_MI2S_TX Channels", value3, 1);
        LOGI("[%d]set TERT_MI2S_TX Channels value:%s, ret:%d", mConfig.deviceId, value3[0], ret);
    }

    mixer_close(mixer);
}


int AudioCapture::mixerSetValue(struct mixer *mixer, const char *control, char **values, unsigned int num_values) {
    struct mixer_ctl *ctl;
    enum mixer_ctl_type type;
    unsigned int num_ctl_values;
    unsigned int i;

    if (isNumber(control))
        ctl = mixer_get_ctl(mixer, atoi(control));
    else
        ctl = mixer_get_ctl_by_name(mixer, control);

    if (!ctl) {
        LOGE("[%d]invalid mixer control: %s", mConfig.deviceId, control);
        return ENOENT;
    }

    type = mixer_ctl_get_type(ctl);
    num_ctl_values = mixer_ctl_get_num_values(ctl);

    if (isNumber(values[0])) {
        if (num_values == 1) {
            /* Set all values the same */
            int value = atoi(values[0]);
            for (i = 0; i < num_ctl_values; i++) {
                if (mixer_ctl_set_value(ctl, i, value)) {
                    LOGE("[%d]error: invalid value", mConfig.deviceId);
                    return EINVAL;
                }
            }
        }
    } else {
        if (type == MIXER_CTL_TYPE_ENUM) {
            if (num_values != 1) {
                LOGE("[%d]enclose strings in quotes and try again", mConfig.deviceId);
                return EINVAL;
            }
            if (mixer_ctl_set_enum_by_string(ctl, values[0])) {
                LOGE("[%d]error: invalid enum value", mConfig.deviceId);
                return EINVAL;
            }
        }
    }

    return 0;
}

int AudioCapture::isNumber(const char *str) {
    char *end;

    if (str == NULL || strlen(str) == 0)
        return 0;

    strtol(str, &end, 0);
    return strlen(end) == 0;
}
