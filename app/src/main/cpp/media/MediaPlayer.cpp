#include "MediaPlayer.h"

MediaPlayer::MediaPlayer() {
    mVideoPlayer = nullptr;
    mAudioPlayer = nullptr;
    mVideoPlayerCallback = nullptr;
    mAudioPlayerCallback = nullptr;
    mScreenCastSourceCallback = nullptr;
    mWindow = nullptr;
    mCallback = nullptr;
}

MediaPlayer::~MediaPlayer() = default;

void MediaPlayer::configure(Player::Config &config) {
    LOGI("configure");
    mConfig = config;
    ScreenCastSource::instance().configure(nullptr, DEVICE_ID_DP, nullptr);
}

void MediaPlayer::setSurfaceTexture(ASurfaceTexture *surfaceTexture) {
    mSurfaceTexture = surfaceTexture;
}

void MediaPlayer::setWindow(ANativeWindow *window) {
    mWindow = window;
}

void MediaPlayer::setCallback(MediaPlayer::Callback *callback) {
    mCallback = callback;
}

void MediaPlayer::start() {
    LOGI("start");
    int flag = STREAMER_FLAG_STREAMING_VIDEO | STREAMER_FLAG_STREAMING_AUDIO;
    ScreenCastSource::instance().start(nullptr, DEVICE_ID_DP, flag);

    mVideoPlayer = std::make_shared<VideoPlayer>();
    mAudioPlayer = std::make_shared<AudioPlayer>();

    mScreenCastSourceCallback = new ScreenCastSourceCallback(this);
    ScreenCastSource::instance().setCallback(mScreenCastSourceCallback);


    mVideoPlayerCallback = new VideoPlayerCallback(this);
    mAudioPlayerCallback = new AudioPlayerCallback(this);

    mVideoPlayer->Player::setCallback(mVideoPlayerCallback);
    mAudioPlayer->Player::setCallback(mAudioPlayerCallback);

    mVideoPlayer->setSurfaceTexture(mSurfaceTexture);
    mVideoPlayer->setWindow(mWindow);
    mVideoPlayer->configure(mConfig);
    mAudioPlayer->configure(mConfig);

    mVideoPlayer->start();
    mAudioPlayer->start();
}

void MediaPlayer::stop() {
    LOGI("stop");
    mVideoPlayer->stop();
    mAudioPlayer->stop();

    mVideoPlayer->Player::setCallback(nullptr);
    mAudioPlayer->Player::setCallback(nullptr);

    int flag = STREAMER_FLAG_STREAMING_VIDEO | STREAMER_FLAG_STREAMING_AUDIO;
    ScreenCastSource::instance().stop(nullptr, DEVICE_ID_DP, flag);
    ScreenCastSource::instance().setCallback(nullptr);

    delete mVideoPlayerCallback;
    delete mAudioPlayerCallback;
    delete mScreenCastSourceCallback;
}

bool MediaPlayer::isVideoConnected() {
    return mVideoPlayer != nullptr && mVideoPlayer->isConnected();
}

bool MediaPlayer::isAudioConnected() {
    return mAudioPlayer != nullptr && mAudioPlayer->isConnected();
}

void MediaPlayer::setVideoFrameDropThreshold(int threshold) {
    if (mVideoPlayer != nullptr) mVideoPlayer->setFrameDropThreshold(threshold);
}

void MediaPlayer::setAudioFrameDropThreshold(int threshold) {
    if (mAudioPlayer != nullptr) mAudioPlayer->setFrameDropThreshold(threshold);
}

void MediaPlayer::setFocused(bool focused) {
    if (mVideoPlayer != nullptr) mVideoPlayer->setFocused(focused);
    if (mAudioPlayer != nullptr) mAudioPlayer->setFocused(focused);
}

void MediaPlayer::onFrameAvailable() {
    if (mVideoPlayer != nullptr) mVideoPlayer->onFrameAvailable();
}

void MediaPlayer::clearAvailable() {
    if (mVideoPlayer != nullptr) mVideoPlayer->clearAvailable();
}

void MediaPlayer::VideoPlayerCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onVideoOutputFormatChanged(mediaFormat);
    }
}

void MediaPlayer::VideoPlayerCallback::onConnectionChanged(bool connected) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onVideoConnectionChanged(connected);
    }
}

void MediaPlayer::VideoPlayerCallback::onRequestKeyframe(bool enable) {

}

void MediaPlayer::AudioPlayerCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onAudioOutputFormatChanged(mediaFormat);
    }
}

void MediaPlayer::AudioPlayerCallback::onConnectionChanged(bool connected) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onAudioConnectionChanged(connected);
    }
}

void MediaPlayer::AudioPlayerCallback::onRequestKeyframe(bool enable) {

}

void MediaPlayer::ScreenCastSourceCallback::onReceived(const char *data, int size, int mediaType,
                                                       int width, int height, void *pStatus) {

    LOGI("ScreenCastSourceCallback onReceived size:%d, mediaType:%d", size, mediaType);
    auto status = (YvrnetStatus *) pStatus;
    if (status->mediaType == YVRNET_MT_VIDEO) {
        if (mPlayer->mVideoPlayer != nullptr) {
            mPlayer->mVideoPlayer->onReceived(data, size, mediaType, width, height, pStatus);
        }
    } else if (status->mediaType == YVRNET_MT_AUDIO) {
        if (mPlayer->mAudioPlayer != nullptr) {
            mPlayer->mAudioPlayer->onReceived(data, size, mediaType, width, height, pStatus);
        }
    }
}

void MediaPlayer::ScreenCastSourceCallback::onConnectionChanged(bool connected) {

}

void MediaPlayer::ScreenCastSourceCallback::onReceived(const char *data, int size) {

}
