#ifndef SCREENCASTSOURCE_AUDIOQUEUE_H
#define SCREENCASTSOURCE_AUDIOQUEUE_H

#include <memory>
#include <mutex>
#include "Common.h"

#define AUDIO_BUFFER_SIZE (4 * 1024)
#define AUDIO_QUEUE_MAX_SIZE 20


typedef struct {
    uint8_t buffer[AUDIO_BUFFER_SIZE];
    uint32_t size;
    uint64_t timestamp;
} AudioFrame;

typedef struct {
    AudioFrame frames[AUDIO_QUEUE_MAX_SIZE];
    long front;
    long rear;
    long size;
} AudioFrameQueue;

class AudioQueue {
public:
    explicit AudioQueue(bool lock = true);
    ~AudioQueue();
    bool push(uint8_t *buffer, uint32_t size, uint64_t timestamp);
    bool push(AudioFrame *frame);
    bool pop(AudioFrame *frame);
    AudioFrame *peek();
    AudioFrame *pop();
    bool isEmpty();
    bool isFull();
    long length();
    void clear();

private:
    bool pushInternal(uint8_t *buffer, uint32_t size, uint64_t timestamp);
    bool pushInternal(AudioFrame *frame);
    bool popInternal(AudioFrame *frame);
    AudioFrame *peekInternal();
    AudioFrame *popInternal();
    bool isEmptyInternal();
    bool isFullInternal();
    long lengthInternal();

private:
    std::shared_ptr <AudioFrameQueue> mQueue;
    std::mutex mMutex;
    bool mLock;
};

#endif //SCREENCASTSOURCE_AUDIOQUEUE_H
