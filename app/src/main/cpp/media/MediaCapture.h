#ifndef SCREENCASTSOURCE_MEDIACAPTURE_H
#define SCREENCASTSOURCE_MEDIACAPTURE_H


#include "Capture.h"
#include "VideoCapture.h"
#include "AudioCapture.h"

class MediaCapture {
public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onVideoBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) = 0;

        virtual void onVideoOutputFormatChanged(AMediaFormat *mediaFormat) = 0;

        virtual void onAudioBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) = 0;

        virtual void onAudioOutputFormatChanged(AMediaFormat *mediaFormat) = 0;
    };

    class VideoCaptureCallback : public Capture::Callback {
    public:
        explicit VideoCaptureCallback(MediaCapture *capture) { mCapture = capture; };

        void onBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

        void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

    private:
        const MediaCapture *mCapture;
    };

    class AudioCaptureCallback : public Capture::Callback {
    public:
        explicit AudioCaptureCallback(MediaCapture *capture) { mCapture = capture; };

        void onBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

        void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

    private:
        const MediaCapture *mCapture;
    };

public:
    MediaCapture();

    ~MediaCapture();

    void configure(Capture::Config &config);

    void start();

    void stop();

    void startVideo();

    void stopVideo();

    bool isVideoCapturing();

    void startAudio();

    void stopAudio();

    bool isAudioCapturing();

    void setCallback(Callback *callback);

    void requestKeyframe();

    void enableAudioEncoder(bool enabled);

private:
    Callback *mCallback;
    std::shared_ptr<VideoCapture> mVideoCapture;
    std::shared_ptr<AudioCapture> mAudioCapture;
    VideoCaptureCallback *mVideoCaptureCallback;
    AudioCaptureCallback *mAudioCaptureCallback;
    std::mutex mMutex;
};


#endif //SCREENCASTSOURCE_MEDIACAPTURE_H
