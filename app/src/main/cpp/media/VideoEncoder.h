#ifndef SCREENCASTSOURCE_VIDEOENCODER_H
#define SCREENCASTSOURCE_VIDEOENCODER_H

#include "Encoder.h"
#include "graphics/EGLContextEnv.h"

class VideoEncoder : public Encoder {
public:
    VideoEncoder();

    ~VideoEncoder() override;

    void prepare() override;

    void onPrepared() override;

    void onEncoderStart() override;

    void onEncoderStop() override;

    void onReleased() override;

    void setSize(int width, int height);

    void setBitRate(int bitRate);

    void setFrameRate(int frameRate);

    void setIFrameInterval(int interval);

    void setIntraRefreshPeriod(int period);

    ANativeWindow *getWindow();

    void setParameters(const char *name, int32_t value);

private:
    int mWidth;
    int mHeight;
    int mBitRate;
    int mFrameRate;
    int mIFrameInterval;
    int mIntraRefreshPeriod;
    ANativeWindow *mNativeWindow;
};


#endif //SCREENCASTSOURCE_VIDEOENCODER_H
