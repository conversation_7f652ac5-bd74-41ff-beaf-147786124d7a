#include "MediaCapture.h"

MediaCapture::MediaCapture() {
    mCallback = nullptr;

    mAudioCapture = std::make_shared<AudioCapture>();
    mVideoCapture = std::make_shared<VideoCapture>();

    mAudioCaptureCallback = new AudioCaptureCallback(this);
    mVideoCaptureCallback = new VideoCaptureCallback(this);

    mAudioCapture->Capture::setCallback(mAudioCaptureCallback);
    mVideoCapture->Capture::setCallback(mVideoCaptureCallback);
}

MediaCapture::~MediaCapture() {
    mAudioCapture->Capture::setCallback(nullptr);
    mVideoCapture->Capture::setCallback(nullptr);

    delete mAudioCaptureCallback;
    delete mVideoCaptureCallback;
}

void MediaCapture::configure(Capture::Config &config) {
    mAudioCapture->configure(config);
    mVideoCapture->configure(config);
}

void MediaCapture::start() {
    std::lock_guard<std::mutex> lock(mMutex);
    mAudioCapture->start();
    mVideoCapture->start();
}

void MediaCapture::stop() {
    std::lock_guard<std::mutex> lock(mMutex);
    mAudioCapture->stop();
    mVideoCapture->stop();
}

void MediaCapture::startVideo() {
    std::lock_guard<std::mutex> lock(mMutex);
    mVideoCapture->start();
}

void MediaCapture::stopVideo() {
    std::lock_guard<std::mutex> lock(mMutex);
    mVideoCapture->stop();
}

bool MediaCapture::isVideoCapturing() {
    std::lock_guard<std::mutex> lock(mMutex);
    return mVideoCapture->isRunning();
}

void MediaCapture::startAudio() {
    std::lock_guard<std::mutex> lock(mMutex);
    mAudioCapture->start();
}

void MediaCapture::stopAudio() {
    std::lock_guard<std::mutex> lock(mMutex);
    mAudioCapture->stop();
}

bool MediaCapture::isAudioCapturing() {
    std::lock_guard<std::mutex> lock(mMutex);
    return mAudioCapture->isRunning();
}

void MediaCapture::setCallback(MediaCapture::Callback *callback) {
    mCallback = callback;
}

void MediaCapture::requestKeyframe() {
    mVideoCapture->requestKeyframe();
}

void MediaCapture::enableAudioEncoder(bool enabled) {
    mAudioCapture->enableAudioEncoder(enabled);
}

void
MediaCapture::VideoCaptureCallback::onBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    LOGI("VideoCaptureCallback::onBufferAvailable");
    if (mCapture->mCallback != nullptr) {
        //Streamer::onVideoBufferAvailable
        mCapture->mCallback->onVideoBufferAvailable(buffer, bufferInfo);
    }
}

void MediaCapture::VideoCaptureCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mCapture->mCallback != nullptr) {
        mCapture->mCallback->onVideoOutputFormatChanged(mediaFormat);
    }
}

//AudioCaptureCallback::onBufferAvailable
void
MediaCapture::AudioCaptureCallback::onBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) {
    LOGI("AudioCaptureCallback::onBufferAvailable");
    if (mCapture->mCallback != nullptr) {
        //Streamer::onAudioBufferAvailable
        mCapture->mCallback->onAudioBufferAvailable(buffer, bufferInfo);
    }
}

void MediaCapture::AudioCaptureCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mCapture->mCallback != nullptr) {
        mCapture->mCallback->onAudioOutputFormatChanged(mediaFormat);
    }
}
