#include "VideoEncoder.h"
#include "Common.h"
#include <sys/resource.h>
#include <android/native_window.h>

VideoEncoder::VideoEncoder() {
    mWidth = 0;
    mHeight = 0;
    mBitRate = 0;
    mFrameRate = 0;
    mIFrameInterval = 0;
    mIntraRefreshPeriod = 0;
    mNativeWindow = nullptr;
}

VideoEncoder::~VideoEncoder() = default;

void VideoEncoder::prepare() {
    Encoder::prepare();

    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_WIDTH, mWidth);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_HEIGHT, mHeight);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_BIT_RATE, mBitRate);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_FRAME_RATE, mFrameRate * 2);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_I_FRAME_INTERVAL, mIFrameInterval);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_INTRA_REFRESH_PERIOD, mIntraRefreshPeriod);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_COLOR_FORMAT, COLOR_FORMAT_SURFACE);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_BITRATE_MODE, 2);
    AMediaFormat_setInt32(mMediaFormat, "vendor.qti-ext-enc-low-latency.enable", 1);
    //AMediaFormat_setInt32(mMediaFormat, "prepend-sps-pps-to-idr-frames", 1);
}

void VideoEncoder::onPrepared() {
    Encoder::onPrepared();
    AMediaCodec_createInputSurface(mMediaCodec, &mNativeWindow);
}

void VideoEncoder::onEncoderStart() {
    Encoder::onEncoderStart();

    std::string threadName = "video-encoder" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for video encoder thread:%d rc:%d", mId, self, rc);
}

void VideoEncoder::onEncoderStop() {
    Encoder::onEncoderStop();
}

void VideoEncoder::onReleased() {
    Encoder::onReleased();
    if (mNativeWindow != nullptr) {
        ANativeWindow_release(mNativeWindow);
        mNativeWindow = nullptr;
    }
}

void VideoEncoder::setSize(int width, int height) {
    mWidth = width;
    mHeight = height;
}

void VideoEncoder::setBitRate(int bitRate) {
    mBitRate = bitRate;
}

void VideoEncoder::setFrameRate(int frameRate) {
    mFrameRate = frameRate;
}

void VideoEncoder::setIFrameInterval(int interval) {
    mIFrameInterval = interval;
}

void VideoEncoder::setIntraRefreshPeriod(int period) {
    mIntraRefreshPeriod = period;
}

ANativeWindow *VideoEncoder::getWindow() {
    return mNativeWindow;
}

void VideoEncoder::setParameters(const char *name, int32_t value) {
    AMediaFormat_setInt32(mMediaFormat, name, value);
    AMediaCodec_setParameters(mMediaCodec, mMediaFormat);
}
