#ifndef SCREENCASTSINK_AUDIODECODER_H
#define SCREENCASTSINK_AUDIODECODER_H


#include "Decoder.h"

class AudioDecoder : public Decoder {
public:
    AudioDecoder();

    ~AudioDecoder() override;

    void prepare() override;

    void onPrepared() override;

    void onDecoderStart() override;

    void onDecoderStop() override;

    void setChannelCount(int channelCount);

    void setSampleRate(int sampleRate);

private:
    int mChannelCount;
    int mSampleRate;
};


#endif //SCREENCASTSINK_AUDIODECODER_H
