#ifndef SCREENCASTSINK_MEDIAPLAYER_H
#define SCREENCASTSINK_MEDIAPLAYER_H

#include "media/VideoPlayer.h"
#include "media/AudioPlayer.h"
#include "Common.h"

class MediaPlayer {
public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onVideoOutputFormatChanged(AMediaFormat *mediaFormat) = 0;

        virtual void onAudioOutputFormatChanged(AMediaFormat *mediaFormat) = 0;

        virtual void onVideoConnectionChanged(bool connected) = 0;

        virtual void onAudioConnectionChanged(bool connected) = 0;
    };

    class VideoPlayerCallback : public Player::Callback {
    public:
        explicit VideoPlayerCallback(MediaPlayer *player) { mPlayer = player; }

        void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

        void onConnectionChanged(bool connected) override;

        void onRequestKeyframe(bool enable) override;

    private:
        const MediaPlayer *mPlayer;
    };

    class AudioPlayerCallback : public Player::Callback {
    public:
        explicit AudioPlayerCallback(MediaPlayer *player) { mPlayer = player; }

        void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

        void onConnectionChanged(bool connected) override;

        void onRequestKeyframe(bool enable) override;

    private:
        const MediaPlayer *mPlayer;
    };

public:
    MediaPlayer();

    ~MediaPlayer();

    void configure(Player::Config &config);

    void setSurfaceTexture(ASurfaceTexture *surfaceTexture);

    void setWindow(ANativeWindow *window);

    void setCallback(Callback *callback);

    void start();

    void stop();

    bool isVideoConnected();

    bool isAudioConnected();

    void setVideoFrameDropThreshold(int threshold);

    void setAudioFrameDropThreshold(int threshold);

    void setFocused(bool focused);

    void onFrameAvailable();

    void clearAvailable();

private:
    std::shared_ptr<VideoPlayer> mVideoPlayer;
    std::shared_ptr<AudioPlayer> mAudioPlayer;
    VideoPlayerCallback *mVideoPlayerCallback;
    AudioPlayerCallback *mAudioPlayerCallback;
    ASurfaceTexture *mSurfaceTexture;
    ANativeWindow *mWindow;
    Callback *mCallback;
    Player::Config mConfig;
};


#endif //SCREENCASTSINK_MEDIAPLAYER_H
