#ifndef SCREENCASTSOURCE_AUDIOCAPTURE_H
#define SCREENCASTSOURCE_AUDIOCAPTURE_H

#include "Capture.h"
#include "AudioEncoder.h"
#include "AudioQueue.h"
#include <fstream>
#include <map>
#include <aaudio/AAudio.h>

class AudioCapture : public Capture, AudioEncoder, AudioEncoder::Callback {
public:
    AudioCapture();

    ~AudioCapture() override;

    void configure(Config &config) override;

    void start() override;

    void stop() override;

    void enableAudioEncoder(bool enabled);

protected:
    void onStart() override;

    void onStop() override;

    void loop() override;

private:
    void onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

    void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

private:
    void mixerEnabled(bool enabled);

    int mixerSetValue(struct mixer *mixer, const char *control, char **values, unsigned int num_values);

    static int isNumber(const char *str);

private:
    bool mEncoderEnabled;
    struct pcm *mPcm;
    char *mBuffer;
    int mSize;
    int mAudioCapIndex;

//#define DEBUG_AUDIO
#ifdef DEBUG_AUDIO
    std::ofstream mStream;
#endif

    const std::map<int, std::string> SAMPLE_RATE_MAP = {
            {8000,   "KHZ_8"},
            {11025,  "KHZ_11P025"},
            {16000,  "KHZ_16"},
            {22050,  "KHZ_22P05"},
            {32000,  "KHZ_32"},
            {44100,  "KHZ_44P1"},
            {48000,  "KHZ_48"},
            {88200,  "KHZ_88P2"},
            {96000,  "KHZ_96"},
            {176400, "KHZ_176P4"},
            {192000, "KHZ_192"},
            {352800, "KHZ_352P8"},
            {384000, "KHZ_384"}
    };
};

#endif //SCREENCASTSOURCE_AUDIOCAPTURE_H
