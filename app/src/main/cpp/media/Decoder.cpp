#include "Decoder.h"
#include "Common.h"


Decoder::Decoder() {
    mMediaCodec = nullptr;
    mMediaFormat = nullptr;
    mNativeWindow = nullptr;
    mCallback = nullptr;
    mRunning = false;
    mId = 0;
    mMimeType = "";

#ifdef ASYNC_DECODE
    mMediaCodecOnAsyncNotifyCallback.onAsyncInputAvailable = [](AMediaCodec *codec, void *userdata, int32_t index) {
        auto decoder = (Decoder *) userdata;
        decoder->onInputAvailable(codec, index);
    };

    mMediaCodecOnAsyncNotifyCallback.onAsyncOutputAvailable = [](AMediaCodec *codec, void *userdata, int32_t index,
                                                                 AMediaCodecBufferInfo *bufferInfo) {
        auto decoder = (Decoder *) userdata;
        decoder->onOutputAvailable(codec, index, bufferInfo);
    };

    mMediaCodecOnAsyncNotifyCallback.onAsyncFormatChanged = [](AMediaCodec *codec, void *userdata,
                                                               AMediaFormat *format) {
        auto decoder = (Decoder *) userdata;
        decoder->onFormatChanged(codec, format);
    };

    mMediaCodecOnAsyncNotifyCallback.onAsyncError = [](AMediaCodec *codec, void *userdata, media_status_t error,
                                                       int32_t actionCode, const char *detail) {
        auto decoder = (Decoder *) userdata;
        decoder->onError(codec, error, actionCode, detail);
    };
#endif
}

Decoder::~Decoder() {
#ifdef ASYNC_DECODE
    mMediaCodecOnAsyncNotifyCallback.onAsyncInputAvailable = nullptr;
    mMediaCodecOnAsyncNotifyCallback.onAsyncOutputAvailable = nullptr;
    mMediaCodecOnAsyncNotifyCallback.onAsyncFormatChanged = nullptr;
    mMediaCodecOnAsyncNotifyCallback.onAsyncError = nullptr;
#endif
}

void Decoder::prepare() {
    mMediaFormat = AMediaFormat_new();
    AMediaFormat_setString(mMediaFormat, AMEDIAFORMAT_KEY_MIME, mMimeType.c_str());
}

void Decoder::onPrepared() {
}

void Decoder::start() {
    LOGI("[%d]decoder start", mId);
    mMediaCodec = AMediaCodec_createDecoderByType(mMimeType.c_str());
    if (mMediaCodec == nullptr) {
        LOGE("create decoder %s failed", mMimeType.c_str());
        return;
    }

    LOGI("[%d]native window %p", mId, mNativeWindow);
    //mNativeWindow 直接Surface渲染（推荐）
    //标准的Android视频解码渲染流程
    media_status_t status = AMediaCodec_configure(mMediaCodec, mMediaFormat, mNativeWindow, nullptr, 0);
    if (status != AMEDIA_OK) {
        LOGE("media codec %s configure failed", mMimeType.c_str());
        return;
    }

    onPrepared();

#ifdef ASYNC_DECODE
    AMediaCodec_setAsyncNotifyCallback(mMediaCodec, mMediaCodecOnAsyncNotifyCallback, this);
#endif

    status = AMediaCodec_start(mMediaCodec);
    if (status != AMEDIA_OK) {
        LOGE("media codec start failed, mimeType:%s status:%d", mMimeType.c_str(), status);
        return;
    }

    mRunning = true;
    mThread = std::thread(&Decoder::run, this);
    LOGI("[%d]decoder started", mId);
}

void Decoder::stop() {
    LOGI("[%d]decoder stop", mId);
    mRunning = false;
    if (mThread.joinable()) {
        mThread.join();
    }
    LOGI("[%d]decoder thread joined", mId);

    if (mMediaCodec != nullptr) {
        //AMediaCodec_flush(mMediaCodec);
        AMediaCodec_stop(mMediaCodec);
        AMediaCodec_delete(mMediaCodec);
        mMediaCodec = nullptr;
    }

    if (mMediaFormat != nullptr) {
        AMediaFormat_delete(mMediaFormat);
        mMediaFormat = nullptr;
    }

    LOGI("[%d]decoder stopped", mId);
}

long Decoder::dequeueInputBuffer() {
    return AMediaCodec_dequeueInputBuffer(mMediaCodec, 100);
}

long Decoder::dequeueInputBuffer(int64_t timeoutUs) {
    return AMediaCodec_dequeueInputBuffer(mMediaCodec, timeoutUs);
}

bool Decoder::inputBuffer(long index, uint8_t *buffer, uint32_t size, uint64_t timestamp) {
    LOGI("[%d]Decoder::inputBuffer index:%ld, size:%u, timestamp:%ld", mId, index, size, timestamp);
    size_t out_size;
    uint8_t *inputBuffer = AMediaCodec_getInputBuffer(mMediaCodec, index, &out_size);
    std::memcpy(inputBuffer, buffer, size);

    media_status_t status = AMediaCodec_queueInputBuffer(mMediaCodec, index, 0, size, timestamp, 0);

    if (status != AMEDIA_OK) {
        LOGE("AMediaCodec_queueInputBuffer failed, error code:%d", status);
        return false;
    }
    return true;
}

long Decoder::dequeueOutputBuffer(AMediaCodecBufferInfo *info) {
    return AMediaCodec_dequeueOutputBuffer(mMediaCodec, info, 100);
}

uint8_t *Decoder::outputBuffer(long index, size_t *out_size) {
    return AMediaCodec_getOutputBuffer(mMediaCodec, index, out_size);;
}

void Decoder::releaseOutputBuffer(long index) {
    AMediaCodec_releaseOutputBuffer(mMediaCodec, index, false);
}

void Decoder::releaseOutputBuffer(long index, bool b) {
    if (mMediaCodec == nullptr) {
        LOGE("media codec has been released");
        return;
    }

    media_status_t status = AMediaCodec_releaseOutputBuffer(mMediaCodec, index, b);
    if (status != AMEDIA_OK) {
        LOGE("[%d]release output buffer error %d", mId, status);
    }
}

void Decoder::decode() {
}

void Decoder::run() {
    LOGI("[%d]begin codec %s", mId, mMimeType.c_str());
    onDecoderStart();

    while (mRunning) {
        decode();
    }

    onDecoderStop();
    LOGI("[%d]end codec %s", mId, mMimeType.c_str());
}

void Decoder::onDecoderStart() {
}

void Decoder::onDecoderStop() {
}

void Decoder::setCallback(Decoder::Callback *callback) {
    mCallback = callback;
}

ANativeWindow *Decoder::getWindow() {
    return mNativeWindow;
}

void Decoder::setWindow(ANativeWindow *window) {
    mNativeWindow = window;
}

void Decoder::setBuffer(const char *name, const void *data, size_t size) {
    AMediaFormat_setBuffer(mMediaFormat, name, data, size);
}

void Decoder::setId(int id) {
    mId = id;
}

void Decoder::setMimeType(std::string type) {
    mMimeType = type;
}

void Decoder::onInputAvailable(AMediaCodec *codec, int32_t index) {

}

void Decoder::onOutputAvailable(AMediaCodec *codec, int32_t index, AMediaCodecBufferInfo *bufferInfo) {

}

void Decoder::onFormatChanged(AMediaCodec *codec, AMediaFormat *format) {

}

void Decoder::onError(AMediaCodec *codec, media_status_t error, int32_t actionCode, const char *detail) {

}
