#include "Encoder.h"
#include "Common.h"

Encoder::Encoder() {
    mMimeType = "";
    mMediaCodec = nullptr;
    mMediaFormat = nullptr;
    mCallback = nullptr;
    mRunning = false;
    mId = 0;
}

Encoder::~Encoder() = default;

void Encoder::prepare() {
    mMediaFormat = AMediaFormat_new();
    AMediaFormat_setString(mMediaFormat, AMEDIAFORMAT_KEY_MIME, mMimeType.c_str());
}

void Encoder::onPrepared() {
}

void Encoder::start() {
    prepare();

    mMediaCodec = AMediaCodec_createEncoderByType(mMimeType.c_str());
    if (mMediaCodec == nullptr) {
        LOGE("[%d]media codec create failed, codec:%s", mId, mMimeType.c_str());
        return;
    }

    char* name = nullptr;
    AMediaCodec_getName(mMediaCodec, &name);
    LOGE("[%d]media codec configure %s", mId, name);

    media_status_t status = AMediaCodec_configure(mMediaCodec, mMediaFormat, nullptr, nullptr,
                                                  AMEDIACODEC_CONFIGURE_FLAG_ENCODE);
    if (status != AMEDIA_OK) {
        LOGE("[%d]media codec configure failed", mId);
        return;
    }

    onPrepared();

    AMediaCodec_start(mMediaCodec);

    mRunning = true;
    mThread = std::thread(&Encoder::run, this);
}

void Encoder::stop() {
    mRunning = false;
    if (mThread.joinable()) {
        mThread.join();
    }

    if (mMediaCodec != nullptr) {
        //AMediaCodec_flush(mMediaCodec);
        AMediaCodec_stop(mMediaCodec);
        AMediaCodec_delete(mMediaCodec);
        mMediaCodec = nullptr;
    }

    if (mMediaFormat != nullptr) {
        AMediaFormat_delete(mMediaFormat);
        mMediaFormat = nullptr;
    }

    onReleased();
}

void Encoder::onReleased() {
}

long Encoder::dequeueInputBuffer() {
    return AMediaCodec_dequeueInputBuffer(mMediaCodec, 1000);
}

long Encoder::dequeueInputBuffer(int64_t timeoutUs) {
    return AMediaCodec_dequeueInputBuffer(mMediaCodec, timeoutUs);
}

int Encoder::inputBuffer(long index, uint8_t *buffer, uint32_t size, uint64_t timestamp) {
    size_t out_size;
    uint8_t *inputBuffer = AMediaCodec_getInputBuffer(mMediaCodec, index, &out_size);
    if (out_size < size) {
        LOGE("invalid input size:%u, buffer size:%u", size, out_size);
        return false;
    }

    std::memcpy(inputBuffer, buffer, size);
    media_status_t status = AMediaCodec_queueInputBuffer(mMediaCodec, index, 0, size, timestamp, 0);
    if (status != AMEDIA_OK) {
        LOGE("[%d]AMediaCodec_queueInputBuffer failed, error code:%d", mId, status);
        return false;
    }

    return true;
}

long Encoder::dequeueOutputBuffer(AMediaCodecBufferInfo *info) {
    return AMediaCodec_dequeueOutputBuffer(mMediaCodec, info, 100);
}

uint8_t *Encoder::outputBuffer(long index, size_t *out_size) {
    return AMediaCodec_getOutputBuffer(mMediaCodec, index, out_size);;
}

void Encoder::releaseOutputBuffer(int index) {
    AMediaCodec_releaseOutputBuffer(mMediaCodec, index, false);
}

void Encoder::process() {
    AMediaCodecBufferInfo bufferInfo;
    long index = AMediaCodec_dequeueOutputBuffer(mMediaCodec, &bufferInfo, 1000);
    if (index >= 0) {
        size_t size;
        uint8_t *buffer = AMediaCodec_getOutputBuffer(mMediaCodec, index, &size);
        if (mCallback != nullptr) {
            mCallback->onOutputBufferAvailable(buffer, &bufferInfo);
        }
        AMediaCodec_releaseOutputBuffer(mMediaCodec, index, false);
    } else if (index == AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED) {
        LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED", mId);
    } else if (index == AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED) {
        auto format = AMediaCodec_getOutputFormat(mMediaCodec);
        if (mCallback != nullptr) {
            mCallback->onOutputFormatChanged(format);
        }
    } else if (index == AMEDIACODEC_INFO_TRY_AGAIN_LATER) {
        //LOGW("[%d]AMEDIACODEC_INFO_TRY_AGAIN_LATER", mId);
    }
}

void Encoder::run() {
    LOGI("[%d]begin codec %s", mId, mMimeType.c_str());
    onEncoderStart();

    while (mRunning) {
        process();
    }

    onEncoderStop();
    LOGI("[%d]end codec %s", mId, mMimeType.c_str());
}

void Encoder::onEncoderStart() {
}

void Encoder::onEncoderStop() {
}

void Encoder::setCallback(Encoder::Callback *callback) {
    mCallback = callback;
}

void Encoder::setMimeType(std::string type) {
    mMimeType = type;
}

void Encoder::setId(int id) {
    mId = id;
}
