#ifndef SCREENCASTSINK_PLAYER_H
#define SCREENCASTSINK_PLAYER_H

#include <thread>
#include "media/NdkMediaFormat.h"
#include "connector/IConnector.h"

class Player : public OnEventListener {
public:
    class Config {
    public:
        class Video {
        public:
            int width;
            int height;
            int frameRate;
            int dropThreshold;
            std::string mimeType;
            std::string ip;
            std::string port;

            [[nodiscard]] std::string toString() const;
        };

        class Audio {
        public:
            int channelCount;
            int sampleRate;
            int dropThreshold;
            std::string mimeType;
            std::string ip;
            std::string port;

            [[nodiscard]] std::string toString() const;
        };

        int id;
        int trace;
        std::string versionName;
        long buildTime;
        Video video;
        Audio audio;
    };

    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onOutputFormatChanged(AMediaFormat *mediaFormat) = 0;

        virtual void onConnectionChanged(bool connected) = 0;
    };
public:
    Player();

    virtual ~Player();

    virtual void start();

    virtual void stop();

    virtual bool isRunning();

    virtual bool isConnected();

    void setFocused(bool focused);

    virtual void configure(Player::Config &config);

    virtual void setCallback(Player::Callback *callback);

    virtual void setAddress(std::string ip, std::string port);

protected:
    void onReceived(const char *data, int size) override;

    virtual void onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus);

    void onConnectionChanged(bool connected) override;

    virtual void run();

    virtual void render();

    virtual void onStart();

    virtual void onStop();

protected:
    bool mRunning;
    std::thread mThread;
    Config mConfig;
    Callback *mCallback;
    std::string mIp;
    std::string mPort;
    std::shared_ptr<IConnector> mConnector;
    bool mFocused;
};


#endif //SCREENCASTSINK_PLAYER_H
