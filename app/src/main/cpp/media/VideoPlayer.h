#ifndef SCREENCASTSINK_VIDEOPLAYER_H
#define SCREENCASTSINK_VIDEOPLAYER_H

#include "Player.h"
#include "utils/CircularQueue.hpp"
#include "connector/IConnector.h"
#include "VideoDecoder.h"
#include "Common.h"
#include "VideoPlayerQueue.h"
#include <fstream>
#include <queue>
#include <android/surface_texture.h>

class VideoPlayer : public Player, public VideoDecoder {
public:
    VideoPlayer();

    ~VideoPlayer() override;

    void start() override;

    void stop() override;

    void configure(Player::Config &config) override;

    void setFrameDropThreshold(int threshold);

    void setSurfaceTexture(ASurfaceTexture *surfaceTexture);

    void onFrameAvailable();

    void clearAvailable();

protected:
    void onReceived(const char *data, int size) override;

    void onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) override;

    void onConnectionChanged(bool connected) override;

    void onRequestKeyframe(bool enable) override;

    void onStart() override;

    void onStop() override;

    void render() override;

    void decode() override;

    void onInputAvailable(AMediaCodec *codec, int32_t index) override;

    void onOutputAvailable(AMediaCodec *codec, int32_t index, AMediaCodecBufferInfo *bufferInfo) override;

    void onFormatChanged(AMediaCodec *codec, AMediaFormat *format) override;

    void onError(AMediaCodec *codec, media_status_t error, int32_t actionCode, const char *detail) override;

private:
    void releaseOutputBuffer();

private:
    VideoPlayerQueue mVideoPlayerQueue;
    CircularQueue<long, 20> mRenderQueue;

#ifdef ASYNC_DECODE
    CircularQueue<long, 20> mInputQueue;
#endif

    std::ofstream mOfstream;

    typedef enum {
        SURFACE_STATE_IDLE = 0,
        SURFACE_STATE_RENDERING,
        SURFACE_STATE_AVAILABLE
    } SurfaceState;
    SurfaceState mSurfaceState;
    std::mutex mMutex;
    ASurfaceTexture *mSurfaceTexture;
};


#endif //SCREENCASTSINK_VIDEOPLAYER_H
