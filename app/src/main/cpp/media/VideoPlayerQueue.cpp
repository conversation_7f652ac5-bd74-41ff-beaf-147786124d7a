#include "VideoPlayerQueue.h"
#include "Common.h"

VideoPlayerQueue::VideoPlayerQueue(bool lock) {
    mQueue = std::make_shared<VideoFrameQueue>();
    mQueue->front = 0;
    mQueue->rear = 0;
    mQueue->size = 0;
    mLock = lock;
}

VideoPlayerQueue::~VideoPlayerQueue() {
    mQueue.reset();
}

bool VideoPlayerQueue::push(uint8_t *buffer, uint32_t size, uint32_t type) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(buffer, size, type);
    }
    return pushInternal(buffer, size, type);
}

bool VideoPlayerQueue::push(uint8_t *buffer, uint32_t size, uint32_t type, int frameId) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(buffer, size, type, frameId);
    }
    return pushInternal(buffer, size, type, frameId);
}

bool VideoPlayerQueue::pushInternal(uint8_t *buffer, uint32_t size, uint32_t type) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, buffer, size);
    videoFrame->size = size;
    videoFrame->type = type;
    return true;
}

bool VideoPlayerQueue::pushInternal(uint8_t *buffer, uint32_t size, uint32_t type, int frameId) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, buffer, size);
    videoFrame->size = size;
    videoFrame->type = type;
    videoFrame->frameId = frameId;
    return true;
}

bool VideoPlayerQueue::push(VideoFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(frame);
    }
    return pushInternal(frame);
}

bool VideoPlayerQueue::pushInternal(VideoFrame *frame) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, frame->buffer, frame->size);
    videoFrame->size = frame->size;
    videoFrame->type = frame->type;
    return true;
}

bool VideoPlayerQueue::pop(VideoFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal(frame);
    }
    return popInternal(frame);
}

bool VideoPlayerQueue::popInternal(VideoFrame *frame) {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return false;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front++]);

    memcpy(frame->buffer, videoFrame->buffer, videoFrame->size);
    frame->size = videoFrame->size;
    frame->type = videoFrame->type;
    return true;
}

VideoFrame *VideoPlayerQueue::peek() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return peekInternal();
    }
    return peekInternal();
}

VideoFrame *VideoPlayerQueue::peekInternal() {
    if (mQueue->size <= 0) {
        return nullptr;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front]);
    return videoFrame;
}

VideoFrame *VideoPlayerQueue::pop() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal();
    }
    return popInternal();
}

VideoFrame *VideoPlayerQueue::popInternal() {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return nullptr;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front++]);
    return videoFrame;
}

bool VideoPlayerQueue::isEmpty() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isEmptyInternal();
    }
    return isEmptyInternal();
}

bool VideoPlayerQueue::isEmptyInternal() {
    return mQueue->size <= 0;
}

bool VideoPlayerQueue::isFull() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isFullInternal();
    }
    return isFullInternal();
}

bool VideoPlayerQueue::isFullInternal() {
    return mQueue->size >= VIDEO_QUEUE_MAX_SIZE;
}

long VideoPlayerQueue::length() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return lengthInternal();
    }
    return lengthInternal();
}

long VideoPlayerQueue::lengthInternal() {
    return mQueue->size;
}

void VideoPlayerQueue::clear() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        mQueue->size = 0;
        mQueue->front = 0;
        mQueue->rear = 0;
        return;
    }

    mQueue->size = 0;
    mQueue->front = 0;
    mQueue->rear = 0;
}
