#ifndef SCREENCASTSINK_VideoPlayQueue_H
#define SCREENCASTSINK_VideoPlayQueue_H

#include <memory>
#include <mutex>
#include "Common.h"

#define VIDEO_BUFFER_SIZE (6 * 1024 * 1024)
#define VIDEO_PLAYER_QUEUE_MAX_SIZE 40


typedef struct {
    uint8_t buffer[VIDEO_BUFFER_SIZE];
    uint32_t size;
    uint32_t type;
    int frameId;
} VideoPlayFrame;

typedef struct {
    VideoPlayFrame frames[VIDEO_PLAYER_QUEUE_MAX_SIZE];
    long front;
    long rear;
    long size;
} VideoPlayFrameQueue;

class VideoPlayerQueue {
public:
    explicit VideoPlayerQueue(bool lock = true);
    ~VideoPlayerQueue();
    bool push(uint8_t *buffer, uint32_t size, uint32_t type);
    bool push(uint8_t *buffer, uint32_t size, uint32_t type, int frameId);
    bool push(VideoPlayFrame *frame);
    bool pop(VideoPlayFrame *frame);
    VideoPlayFrame *peek();
    VideoPlayFrame *pop();
    bool isEmpty();
    bool isFull();
    long length();
    void clear();

private:
    bool pushInternal(uint8_t *buffer, uint32_t size, uint32_t type);
    bool pushInternal(uint8_t *buffer, uint32_t size, uint32_t type, int frameId);
    bool pushInternal(VideoPlayFrame *frame);
    bool popInternal(VideoPlayFrame *frame);
    VideoPlayFrame *peekInternal();
    VideoPlayFrame *popInternal();
    bool isEmptyInternal();
    bool isFullInternal();
    long lengthInternal();

private:
    std::shared_ptr <VideoPlayFrameQueue> mQueue;
    std::mutex mMutex;
    bool mLock;
};

#endif //SCREENCASTSINK_VideoPlayQueue_H
