#include "AudioQueue.h"
#include "Common.h"

AudioQueue::AudioQueue(bool lock) {
    mQueue = std::make_shared<AudioFrameQueue>();
    mQueue->front = 0;
    mQueue->rear = 0;
    mQueue->size = 0;
    mLock = lock;
}

AudioQueue::~AudioQueue() {
    mQueue.reset();
}

bool AudioQueue::push(int frameId, uint8_t *buffer, uint32_t size, AudioConfig config) {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return pushInternal(frameId, buffer, size, config);
    }
    return pushInternal(frameId, buffer, size, config);
}

bool AudioQueue::pushInternal(int frameId, uint8_t *buffer, uint32_t size, AudioConfig config) {
    if (mQueue->size >= AUDIO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->rear++]);

    audioFrame->frameId = frameId;
    memcpy(audioFrame->buffer, buffer, size);
    audioFrame->size = size;
    audioFrame->config = config;

    mCondition.notify_one();
    return true;
}

bool AudioQueue::push(AudioFrame *frame) {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return pushInternal(frame);
    }
    return pushInternal(frame);
}

bool AudioQueue::pushInternal(AudioFrame *frame) {
    if (mQueue->size >= AUDIO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->rear++]);

    audioFrame->frameId = frame->frameId;
    memcpy(audioFrame->buffer, frame->buffer, frame->size);
    audioFrame->size = frame->size;
    audioFrame->config = frame->config;
    return true;
}

bool AudioQueue::pop(AudioFrame *frame) {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return popInternal(frame);
    }
    return popInternal(frame);
}

bool AudioQueue::popInternal(AudioFrame *frame) {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return false;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front++]);

    frame->frameId = audioFrame->frameId;
    memcpy(frame->buffer, audioFrame->buffer, audioFrame->size);
    frame->size = audioFrame->size;
    frame->config = audioFrame->config;
    return true;
}

AudioFrame *AudioQueue::peek() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        mCondition.wait(lock, [this]() { return mQueue->size > 0; });
        return peekInternal();
    }
    return peekInternal();
}

AudioFrame *AudioQueue::peekInternal() {
    if (mQueue->size <= 0) {
        return nullptr;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front]);
    return audioFrame;
}

AudioFrame *AudioQueue::pop() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        mCondition.wait(lock, [this]() { return mQueue->size > 0; });
        return popInternal();
    }
    return popInternal();
}

AudioFrame *AudioQueue::popInternal() {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return nullptr;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front++]);
    return audioFrame;
}

bool AudioQueue::isEmpty() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return isEmptyInternal();
    }
    return isEmptyInternal();
}

bool AudioQueue::isEmptyInternal() {
    return mQueue->size <= 0;
}

bool AudioQueue::isFull() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return isFullInternal();
    }
    return isFullInternal();
}

bool AudioQueue::isFullInternal() {
    return mQueue->size >= AUDIO_QUEUE_MAX_SIZE;
}

long AudioQueue::length() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        return lengthInternal();
    }
    return lengthInternal();
}

long AudioQueue::lengthInternal() {
    return mQueue->size;
}

void AudioQueue::clear() {
    if (mLock) {
        std::unique_lock<std::mutex> lock(mMutex);
        mQueue->size = 0;
        mQueue->front = 0;
        mQueue->rear = 0;
        return;
    }

    mQueue->size = 0;
    mQueue->front = 0;
    mQueue->rear = 0;
}
