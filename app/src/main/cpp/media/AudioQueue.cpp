#include "AudioQueue.h"
#include "Common.h"

AudioQueue::AudioQueue(bool lock) {
    mQueue = std::make_shared<AudioFrameQueue>();
    mQueue->front = 0;
    mQueue->rear = 0;
    mQueue->size = 0;
    mLock = lock;
}

AudioQueue::~AudioQueue() {
    mQueue.reset();
}

bool AudioQueue::push(uint8_t *buffer, uint32_t size, uint64_t timestamp) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(buffer, size, timestamp);
    }
    return pushInternal(buffer, size, timestamp);
}

bool AudioQueue::pushInternal(uint8_t *buffer, uint32_t size, uint64_t timestamp) {
    if (mQueue->size >= AUDIO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(audioFrame->buffer, buffer, size);
    audioFrame->size = size;
    audioFrame->timestamp = timestamp;
    return true;
}

bool AudioQueue::push(AudioFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(frame);
    }
    return pushInternal(frame);
}

bool AudioQueue::pushInternal(AudioFrame *frame) {
    if (mQueue->size >= AUDIO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(audioFrame->buffer, frame->buffer, frame->size);
    audioFrame->size = frame->size;
    audioFrame->timestamp = frame->timestamp;
    return true;
}

bool AudioQueue::pop(AudioFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal(frame);
    }
    return popInternal(frame);
}

bool AudioQueue::popInternal(AudioFrame *frame) {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return false;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front++]);

    memcpy(frame->buffer, audioFrame->buffer, audioFrame->size);
    frame->size = audioFrame->size;
    frame->timestamp = audioFrame->timestamp;
    return true;
}

AudioFrame *AudioQueue::peek() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return peekInternal();
    }
    return peekInternal();
}

AudioFrame *AudioQueue::peekInternal() {
    if (mQueue->size <= 0) {
        return nullptr;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front]);
    return audioFrame;
}

AudioFrame *AudioQueue::pop() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal();
    }
    return popInternal();
}

AudioFrame *AudioQueue::popInternal() {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return nullptr;
    }

    if (mQueue->front >= AUDIO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    AudioFrame *audioFrame = &(mQueue->frames[mQueue->front++]);
    return audioFrame;
}

bool AudioQueue::isEmpty() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isEmptyInternal();
    }
    return isEmptyInternal();
}

bool AudioQueue::isEmptyInternal() {
    return mQueue->size <= 0;
}

bool AudioQueue::isFull() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isFullInternal();
    }
    return isFullInternal();
}

bool AudioQueue::isFullInternal() {
    return mQueue->size >= AUDIO_QUEUE_MAX_SIZE;
}

long AudioQueue::length() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return lengthInternal();
    }
    return lengthInternal();
}

long AudioQueue::lengthInternal() {
    return mQueue->size;
}

void AudioQueue::clear() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        mQueue->size = 0;
        mQueue->front = 0;
        mQueue->rear = 0;
        return;
    }

    mQueue->size = 0;
    mQueue->front = 0;
    mQueue->rear = 0;
}
