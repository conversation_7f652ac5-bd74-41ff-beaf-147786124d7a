#include "AudioPlayer.h"
#include "AudioDecoder.h"
#include "VideoDecoder.h"
#include "Common.h"
#include "utils/Statistics.hpp"
#include <sys/resource.h>

#define BYPASS_ENCODER

AudioPlayer::AudioPlayer() {
    mBuilder = nullptr;
    mAAudioStream = nullptr;
}

AudioPlayer::~AudioPlayer() = default;

void AudioPlayer::start() {
    LOGI("[%d]audio player start", mId);
    std::lock_guard<std::mutex> lock(mMutex);
    mQueue.clear();
    mRenderQueue.clear();
    Statistics::Instance().stop(mId, DECODER_AUDIO);
    Statistics::Instance().setMaxFrame(mId, DECODER_AUDIO, AUDIO_BUFFER_SIZE);
    Statistics::Instance().setNetDevice(mId, DECODER_AUDIO, mConnector.get());

#ifndef BYPASS_ENCODER
    AudioDecoder::setId(mId);
    AudioDecoder::prepare();
    AudioDecoder::start();
#endif

    Player::start();

    LOGI("[%d]audio player started", mId);
}

void AudioPlayer::stop() {
    LOGI("[%d]audio player stop", mId);
    std::lock_guard<std::mutex> lock(mMutex);

    Player::mRunning = false;
    mQueue.push(0, nullptr, 0, {0, 0});
    Player::stop();

    Statistics::Instance().stop(mId, DECODER_AUDIO);
#ifndef BYPASS_ENCODER
    AudioDecoder::stop();
#endif

    LOGI("[%d]audio player stopped", mId);
}

void AudioPlayer::configure(Player::Config &config) {
    Player::configure(config);
    Player::setAddress(config.audio.ip, config.audio.port);

    AudioDecoder::setId(config.id);
    AudioDecoder::setMimeType(config.audio.mimeType);
    AudioDecoder::setChannelCount(config.audio.channelCount);
    AudioDecoder::setSampleRate(config.audio.sampleRate);

    LOGI("[%d]audio configure:%s", config.id, config.audio.toString().c_str());
}

void AudioPlayer::setFrameDropThreshold(int threshold) {
    mConfig.audio.dropThreshold = threshold;
    LOGI("[%d]set audio frame drop threshold value:%d", mId, threshold);
}

void AudioPlayer::onReceived(const char *data, int size) {
    // LOGI("on received size:%d", size);
    if (size == sizeof(YvrnetStatus) && data) {
        YvrnetStatus *pStatic = (YvrnetStatus *) data;
        if (pStatic->u64InterTime != 0) {
            Statistics::Instance().sendFrame(mId, DECODER_AUDIO, pStatic->sendID,pStatic->u64SendTime);
            Statistics::Instance().totalFrame(mId, DECODER_AUDIO, pStatic->sendID, pStatic->u64FrameBeginTime);
        }
    }
}

//AudioPlayer::onReceived
void AudioPlayer::onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) {
    static int count = 0;
    count++;
    if (count > 300) {
        LOGI("[%d]on audio data received, size:%d", mId, size);
        count = 0;
    }

    auto status = (YvrnetStatus *) pStatus;
    Statistics::Instance().totalFrameIn(mId, DECODER_AUDIO, status->sendID, status->u64CapTime);
    Statistics::Instance().capFrame(mId, DECODER_AUDIO, status->sendID, status->u64CapTime);
    Statistics::Instance().sendFrameIn(mId, DECODER_AUDIO, status->sendID, status->u64SendTime);
    Statistics::Instance().encFrame(mId, DECODER_AUDIO, status->sendID, status->u64EncTime, size);

#ifndef BYPASS_ENCODER
    std::shared_ptr<AudioData> audioData = std::make_shared<AudioData>();
    std::memcpy(audioData->buffer, data, size);
    audioData->size = size;
#else
    Statistics::Instance().playFrameIn(mId, DECODER_AUDIO, status->sendID, getCurrentTimestampUs());
#endif

#ifdef DEBUG_AUDIO
    if (mStream.is_open()) {
        mStream.write(reinterpret_cast<const char *>(data), size);
    }
#endif

    //mQueue.push
    if (!mQueue.push(status->sendID, (uint8_t *) data, size, {status->reserved1, status->reserved2})) {
        LOGW("[%d]push audio data failed.", mId);
    }
}

void AudioPlayer::onConnectionChanged(bool connected) {
    Player::onConnectionChanged(connected);
    Statistics::Instance().netConnect(mId, DECODER_AUDIO, connected);
    if (!connected) {
        Statistics::Instance().stop(mId, DECODER_AUDIO);
    }
}

void AudioPlayer::onStart() {
    Player::onStart();
    LOGI("[%d]audio player on start", mId);

    std::string threadName = "audio-player" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for audio player thread:%d rc:%d", mId, self, rc);

    while (isRunning()) {
        auto audioData = mQueue.pop();
        if (audioData->size == 0) {
            continue;
        }

        LOGI("[%d]audio source version:%s, build time:%ld", mConfig.id, mConfig.versionName.c_str(), mConfig.buildTime);
        if (mConfig.buildTime >= 1740981753) { // rc  version:Box_1.0.1.16  time:Mon Mar 3 14:02:33 CST 2025
            if (audioData->config.channelCount >= 1 && audioData->config.channelCount <= 8) {
                mConfig.audio.channelCount = audioData->config.channelCount;
            } else {
                LOGW("invalid channel count %d", audioData->config.channelCount);
            }

            if (audioData->config.sampleRate >= 8000 && audioData->config.sampleRate <= 384000) {
                mConfig.audio.sampleRate = audioData->config.sampleRate;
            } else {
                LOGW("invalid sample rate %d", audioData->config.sampleRate);
            }

            LOGI("[%d]update audio configure:%s", mConfig.id, mConfig.audio.toString().c_str());
        }

        break;
    }

    aaudio_result_t result = AAudio_createStreamBuilder(&mBuilder);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio create stream builder error: %s", mId, AAudio_convertResultToText(result));
        return;
    }

    AAudioStreamBuilder_setDeviceId(mBuilder, AAUDIO_UNSPECIFIED);
    AAudioStreamBuilder_setSampleRate(mBuilder, mConfig.audio.sampleRate);
    AAudioStreamBuilder_setChannelCount(mBuilder, 2);
    AAudioStreamBuilder_setFormat(mBuilder, AAUDIO_FORMAT_PCM_I16);
    AAudioStreamBuilder_setUsage(mBuilder, AAUDIO_USAGE_MEDIA);
    AAudioStreamBuilder_setContentType(mBuilder, AAUDIO_CONTENT_TYPE_MUSIC);
    AAudioStreamBuilder_setDirection(mBuilder, AAUDIO_DIRECTION_OUTPUT);
    AAudioStreamBuilder_setBufferCapacityInFrames(mBuilder, 4096);
    AAudioStreamBuilder_setErrorCallback(mBuilder, onAudioStreamError, this);
    //AAudioStreamBuilder_setPerformanceMode(mBuilder, AAUDIO_PERFORMANCE_MODE_LOW_LATENCY);

    result = AAudioStreamBuilder_openStream(mBuilder, &mAAudioStream);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio open stream error: %s", mId, AAudio_convertResultToText(result));
    }

    printAudioStreamInfo(mAAudioStream);
    result = AAudioStream_requestStart(mAAudioStream);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio request start error: %s", mId, AAudio_convertResultToText(result));
    }

#ifdef DEBUG_AUDIO
    std::string name = "sdcard/" + std::to_string(mId) + "audio.stream";
    mStream.open(name.c_str(), std::ios::out | std::ios::binary);
    if (!mStream.is_open()) {
        LOGE("open audio.stream failed, error %s", std::strerror(errno));
    }
#endif
}

void AudioPlayer::onStop() {
    Player::onStop();
    LOGI("[%d]audio player on stop", mId);

    aaudio_result_t result = AAudioStream_requestStop(mAAudioStream);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio request stop error: %s", mId, AAudio_convertResultToText(result));
    }
    LOGI("[%d]AAudio request stop", mId);

    result = AAudioStream_close(mAAudioStream);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio close stream error: %s", mId, AAudio_convertResultToText(result));
    }
    LOGI("[%d]AAudio closed", mId);

    result = AAudioStreamBuilder_delete(mBuilder);
    if (result != AAUDIO_OK) {
        LOGE("[%d]AAudio delete stream error: %s", mId, AAudio_convertResultToText(result));
    }
    LOGI("[%d]AAudio deleted", mId);

#ifdef DEBUG_AUDIO
    mStream.close();
#endif
}

void AudioPlayer::render() {
    Player::render();

    if (!isRunning()) {
        return;
    }

#ifndef BYPASS_ENCODER
    std::shared_ptr<AudioData> audioData;

    unsigned long length = mRenderQueue.size();
    if (length == 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

    while (length > 10) {
        if (!mRenderQueue.tryPop(audioData)) {
            LOGW("[%d]try pop audio data error, continue", mId);
            continue;
        }

        Statistics::Instance().playFrameOut(mId, DECODER_AUDIO, true);
        LOGW("[%d]discard old audio frame, length:%ld", mId, length);
        length--;
    }

    if (!mRenderQueue.tryPop(audioData)) {
        LOGW("[%d]try pop audio data error", mId);
        return;
    }

    int32_t numFrames = audioData->size / 4;
    aaudio_result_t result = AAudioStream_write(mAAudioStream, audioData->buffer,
                                                numFrames,
                                                20 * 1000 * 1000L);
    if (numFrames != result) {
        LOGW("[%d]frameCount %d  result:%d", mId, numFrames, result);
    }else {
        Statistics::Instance().playFrameOut(mId, DECODER_AUDIO, false);
    }
#else

    //mQueue.pop()
    AudioFrame *audioData = mQueue.pop();
    if (audioData->size == 0) {
        return;
    }

    unsigned long length = mQueue.length();
    if (length > mConfig.audio.dropThreshold) {
        LOGW("[%d]discard old audio frame, length:%ld", mId, length);
        Statistics::Instance().playFrameOut(mId, DECODER_AUDIO, true);
        return;
    }

    if (!mFocused) {
        Statistics::Instance().playFrameOut(mId, DECODER_AUDIO, false);
        return;
    }

    int32_t numFrames = audioData->size / 4;
    aaudio_result_t result = AAudioStream_write(mAAudioStream, audioData->buffer,
                                                numFrames,
                                                10 * 1000 * 1000L);

    if (numFrames != result) {
        LOGW("[%d]frameCount %d result:%d", mId, numFrames, result);
    } else {
        Statistics::Instance().playFrameOut(mId, DECODER_AUDIO, false);
    }

#endif
}

void AudioPlayer::decode() {
    Decoder::decode();

    if (!isRunning()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        return;
    }

#ifndef BYPASS_ENCODER
    long index;
    do {
        AudioFrame *audioData = mQueue.peek();
        if (audioData == nullptr) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            break;
        }

        index = AudioDecoder::dequeueInputBuffer(100);
        if (index >= 0) {
            audioData = mQueue.pop();

            long microseconds = getCurrentTimestampUs();
            bool bRet = AudioDecoder::inputBuffer(index, audioData->buffer, audioData->size, microseconds);
            if (bRet) {
                Statistics::Instance().decFrameIn(mId, DECODER_AUDIO, audioData->frameId, microseconds);
            }
        }
    } while (index >= 0);

    do {
        AMediaCodecBufferInfo bufferInfo;
        index = AudioDecoder::dequeueOutputBuffer(&bufferInfo);
        if (index >= 0) {
            size_t size;
            uint8_t *outBuffer = AudioDecoder::outputBuffer(index, &size);

            std::shared_ptr<AudioData> outData = std::make_shared<AudioData>();
            std::memcpy(outData->buffer, outBuffer, bufferInfo.size);
            outData->size = bufferInfo.size;
            outData->timestamp = bufferInfo.presentationTimeUs;

            if (!mRenderQueue.tryPush(outData)) {
                LOGW("[%d]try push audio out data failed", mId);
            }

            AudioDecoder::releaseOutputBuffer(index);
            int frameId = Statistics::Instance().decFrameOut(mId, DECODER_AUDIO, STATIS_INVALID_ID, bufferInfo.presentationTimeUs);
            Statistics::Instance().playFrameIn(mId, DECODER_AUDIO, frameId, getCurrentTimestampUs());
        } else if (index == AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED) {
            LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_BUFFERS_CHANGED", mId);
        } else if (index == AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED) {
            LOGW("[%d]AMEDIACODEC_INFO_OUTPUT_FORMAT_CHANGED", mId);
            if (Player::mCallback != nullptr) {
                auto format = AMediaCodec_getOutputFormat(mMediaCodec);
                Player::mCallback->onOutputFormatChanged(format);
            }
        } else if (index == AMEDIACODEC_INFO_TRY_AGAIN_LATER) {
            //LOGW("AMEDIACODEC_INFO_TRY_AGAIN_LATER");
        }
    } while (index >= 0);
#endif
}

void AudioPlayer::printAudioStreamInfo(const AAudioStream *stream) {
#define STREAM_CALL(c) AAudioStream_##c((AAudioStream*)stream)
    LOGI("------AAudio Stream Information begin------");
    LOGI("StreamID: %p", stream);
    LOGI("BufferCapacity: %d", STREAM_CALL(getBufferCapacityInFrames));
    LOGI("BufferSize: %d", STREAM_CALL(getBufferSizeInFrames));
    LOGI("FramesPerBurst: %d", STREAM_CALL(getFramesPerBurst));
    LOGI("XRunCount: %d", STREAM_CALL(getXRunCount));
    LOGI("SampleRate: %d", STREAM_CALL(getSampleRate));
    LOGI("SamplesPerFrame: %d", STREAM_CALL(getChannelCount));
    LOGI("DeviceId: %d", STREAM_CALL(getDeviceId));
    LOGI("Format: %d", STREAM_CALL(getFormat));
    LOGI("SharingMode: %s", (STREAM_CALL(getSharingMode)) == AAUDIO_SHARING_MODE_EXCLUSIVE ?
                            "EXCLUSIVE" : "SHARED");

    aaudio_performance_mode_t perfMode = STREAM_CALL(getPerformanceMode);
    std::string perfModeDescription;
    switch (perfMode) {
        case AAUDIO_PERFORMANCE_MODE_NONE:
            perfModeDescription = "NONE";
            break;
        case AAUDIO_PERFORMANCE_MODE_LOW_LATENCY:
            perfModeDescription = "LOW_LATENCY";
            break;
        case AAUDIO_PERFORMANCE_MODE_POWER_SAVING:
            perfModeDescription = "POWER_SAVING";
            break;
        default:
            perfModeDescription = "UNKNOWN";
            break;
    }
    LOGI("PerformanceMode: %s", perfModeDescription.c_str());

    aaudio_direction_t dir = STREAM_CALL(getDirection);
    LOGI("Direction: %s", (dir == AAUDIO_DIRECTION_OUTPUT ? "OUTPUT" : "INPUT"));
    if (dir == AAUDIO_DIRECTION_OUTPUT) {
        LOGI("FramesReadByDevice: %d", (int32_t) STREAM_CALL(getFramesRead));
        LOGI("FramesWriteByApp: %d", (int32_t) STREAM_CALL(getFramesWritten));
    } else {
        LOGI("FramesReadByApp: %d", (int32_t) STREAM_CALL(getFramesRead));
        LOGI("FramesWriteByDevice: %d", (int32_t) STREAM_CALL(getFramesWritten));
    }
    LOGI("------AAudio Stream Information end------");
#undef STREAM_CALL
}

void AudioPlayer::onAudioStreamError(AAudioStream *stream, void *userData, aaudio_result_t error) {
    LOGI("on audio stream error:%d", error);
    auto player = (AudioPlayer *) userData;
    std::lock_guard<std::mutex> lock(player->mMutex);

    if (error == AAUDIO_ERROR_DISCONNECTED && player->isRunning()) {
        aaudio_result_t result = AAudioStreamBuilder_openStream(player->mBuilder, &(player->mAAudioStream));
        if (result != AAUDIO_OK) {
            LOGE("[%d]AAudio open stream error: %s", player->mConfig.id, AAudio_convertResultToText(result));
        }

        result = AAudioStream_requestStart(player->mAAudioStream);
        if (result != AAUDIO_OK) {
            LOGE("[%d]AAudio request start error: %s", player->mConfig.id, AAudio_convertResultToText(result));
        }
    }
}
