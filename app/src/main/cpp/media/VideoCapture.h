#ifndef SCREENCASTSOURCE_VIDEOCAPTURE_H
#define SCREENCASTSOURCE_VIDEOCAPTURE_H


#include "Capture.h"
#include "VideoEncoder.h"
#include "CameraDef.h"
#include "utils/CircularBlockingQueue.hpp"
#include "graphics/GraphicsRender.h"
#include "VideoQueue.h"
#include <fstream>


class VideoCapture : public Capture, VideoEncoder, VideoEncoder::Callback, android::yvr::CameraListener {
public:
    VideoCapture();

    ~VideoCapture() override;

    void configure(Config &config) override;

    void start() override;

    void stop() override;

    void requestKeyframe();

protected:
    void onStart() override;

    void onStop() override;

    void loop() override;

private:
    void onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

    void onOutputFormatChanged(AMediaFormat *mediaFormat) override;

    void onFrameAvailable(CameraAHBuffer &buffer, android::yvr::DPDeviceType type) override;

private:
    std::shared_ptr<GraphicsRender> mGraphicsRender;
    std::shared_ptr<EGLContextEnv> mEGLContext;
    int mVideoCapIndex;
    VideoQueue mVideoQueue;
#define DATA_BUFFER_SIZE 4 * 1024 * 1024
    uint8_t mDataBuffer[DATA_BUFFER_SIZE];
    uint8_t *mCodecConfig;
    int mCodecConfigSize;
    int mFrameDropCount;

//#define DEBUG_VIDEO
#ifdef DEBUG_VIDEO
    std::ofstream mStream;
#endif
};

#endif //SCREENCASTSOURCE_VIDEOCAPTURE_H
