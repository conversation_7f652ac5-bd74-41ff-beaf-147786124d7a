#include "Player.h"
#include "Common.h"
#include "connector/YVRNet.h"

Player::Player() {
    mFocused = true;
    mRunning = false;
    mCallback = nullptr;
    mIp = "";
    mPort = "";
    //mConnector = std::make_shared<YVRNet>();
}

Player::~Player() = default;

void Player::start() {
    mRunning = true;
    mThread = std::thread(&Player::run, this);
    LOGE("[%d]Player::start() - TEST LOG", mConfig.id);

    /*mConnector->setConnectionMode(CONNECTION_MODE_CLIENT);
    mConnector->setIp(mIp);
    mConnector->setPort(mPort);
    mConnector->setOnEventListener(this);
    mConnector->start();*/
    LOGI("[%d]connector started, port:%s", mConfig.id, mPort.c_str());
}

void Player::stop() {
    //mConnector->setOnEventListener(nullptr);
    //mConnector->stop();
    LOGI("[%d]connector stopped, port:%s", mConfig.id, mPort.c_str());

    mRunning = false;
    if (mThread.joinable()) {
        mThread.join();
    }
    LOGI("[%d]player thread joined", mConfig.id);
}

bool Player::isRunning() {
    return mRunning;
}

bool Player::isConnected() {
    return mRunning;//mConnector->isConnected();
}

void Player::setFocused(bool focused) {
    mFocused = focused;
}

void Player::onReceived(const char *data, int size) {
}

void Player::onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) {
}

void Player::onConnectionChanged(bool connected) {
    LOGI("[%d]on connection changed %d, port:%s", mConfig.id, connected, mPort.c_str());
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(connected);
    }
}

void Player::configure(Player::Config &config) {
    mConfig = config;
}

void Player::setCallback(Player::Callback *callback) {
    mCallback = callback;
}

void Player::setAddress(std::string ip, std::string port) {
    mIp = ip;
    mPort = port;
}

void Player::run() {
    onStart();
    while (mRunning) {
        // 在循环中持续调用render方法
        render();
    }

    onStop();
}

//render()的空实现，AudioPlayer和VideoPlayer会重写
void Player::render() {
}

void Player::onStart() {
}

void Player::onStop() {
}

std::string Player::Config::Video::toString() const {
    return "width:" + std::to_string(width)
           + " height:" + std::to_string(height)
           + " frameRate:" + std::to_string(frameRate)
           + " dropThreshold:" + std::to_string(dropThreshold)
           + " mimeType:" + mimeType
           + " ip:" + ip
           + " port:" + port;
}

std::string Player::Config::Audio::toString() const {
    return "channelCount:" + std::to_string(channelCount)
           + " sampleRate:" + std::to_string(sampleRate)
           + " dropThreshold:" + std::to_string(dropThreshold)
           + " mimeType:" + mimeType
           + " ip:" + ip
           + " port:" + port;
}
