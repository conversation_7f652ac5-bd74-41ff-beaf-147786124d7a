#ifndef SCREENCASTSOURCE_CAPTURE_H
#define SCREENCASTSOURCE_CAPTURE_H

#include <thread>
#include <media/NdkMediaFormat.h>
#include <media/NdkMediaCodec.h>


class Capture {
public:
    class Config {
    public:
        class Video {
        public:
            int width;
            int height;
            int bitRate;
            int frameRate;
            int colorFormat;
            int iFrameInterval;
            int intraRefreshPeriod;
            int codec;
            std::string mimeType;

            [[nodiscard]] std::string toString() const;
        };

        class Audio {
        public:
            int bitRate;
            int channelCount;
            int sampleRate;
            int codec;
            std::string mimeType;

            [[nodiscard]] std::string toString() const;
        };

        int deviceId;
        Video video;
        Audio audio;

        [[nodiscard]] std::string toString() const;
    };

public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) = 0;

        virtual void onOutputFormatChanged(AMediaFormat *mediaFormat) = 0;
    };

public:
    Capture();

    virtual ~Capture();

    virtual void configure(Config &config);

    virtual void start();

    virtual void stop();

    virtual bool isRunning();

    virtual void setCallback(Callback *callback);

protected:
    virtual void onStart();

    virtual void onStop();

    virtual void loop();

protected:
    void run();

protected:
    bool mRunning;
    Config mConfig;
    std::thread mThread;
    Callback *mCallback;
};


#endif //SCREENCASTSOURCE_CAPTURE_H
