#ifndef SCREENCASTSINK_VIDEODECODER_H
#define SCREENCASTSINK_VIDEODECODER_H


#include <android/native_window.h>
#include "Decoder.h"

class VideoDecoder : public Decoder {
public:
    VideoDecoder();

    ~VideoDecoder() override;

    void prepare() override;

    void onPrepared() override;

    void onDecoderStart() override;

    void onDecoderStop() override;

    void setSize(int width, int height);

private:
    int mWidth;
    int mHeight;
};


#endif //SCREENCASTSINK_VIDEODECODER_H
