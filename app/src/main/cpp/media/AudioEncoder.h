#ifndef SCREENCASTSOURCE_AUDIOENCODER_H
#define SCREENCASTSOURCE_AUDIOENCODER_H


#include "Encoder.h"


class AudioEncoder : public Encoder {
public:
    AudioEncoder();

    ~AudioEncoder() override;

    void prepare() override;

    void onPrepared() override;

    void onEncoderStart() override;

    void onEncoderStop() override;

    void setBitRate(int bitRate);

    void setChannelCount(int channelCount);

    void setSampleRate(int sampleRate);

private:
    int mBitRate;
    int mChannelCount;
    int mSampleRate;
};


#endif //SCREENCASTSOURCE_AUDIOENCODER_H
