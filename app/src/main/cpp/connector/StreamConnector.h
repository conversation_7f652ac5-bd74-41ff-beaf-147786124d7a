#ifndef SCREENCASTSOURCE_STREAMCONNECTOR_H
#define SCREENCASTSOURCE_STREAMCONNECTOR_H


#include "IConnector.h"

class StreamConnector {
public:
    class Config {
    public:
        std::string videoIp;
        std::string videoPort;
        connection_type_t videoType;
        connection_mode_t videoMode;
        std::string audioIp;
        std::string audioPort;
        connection_type_t audioType;
        connection_mode_t audioMode;
    };

    class Callback {
    public:
        virtual void onVideoConnectionChanged(bool connected) = 0;

        virtual void onVideoReceived(const char *data, int size) = 0;

        virtual void onVideoRequestKeyframe(bool enable) = 0;

        virtual void onAudioConnectionChanged(bool connected) = 0;

        virtual void onAudioReceived(const char *data, int size) = 0;

        virtual void onAudioRequestKeyframe(bool enable) = 0;
    };

    class OnVideoEventListener : public OnEventListener {
    public:
        explicit OnVideoEventListener(StreamConnector *connector) { mConnector = connector; }

        void onConnectionChanged(bool connected) override;

        void onReceived(const char *data, int size) override;

        void onReceived(const char *data, int size, int mediaType, int width, int height, void* pStatus) override;

        void onRequestKeyframe(bool enable) override;

    private:
        StreamConnector *mConnector;

    };

    class OnAudioEventListener : public OnEventListener {
    public:

        explicit OnAudioEventListener(StreamConnector *connector) { mConnector = connector; }

        void onConnectionChanged(bool connected) override;

        void onReceived(const char *data, int size) override;

        void onReceived(const char *data, int size, int mediaType, int width, int height, void* pStatus) override;

        void onRequestKeyframe(bool enable) override;

    private:
        StreamConnector *mConnector;
    };

public:
    StreamConnector();

    ~StreamConnector();

    void configure(Config &config);

    void setCallback(Callback *callback);

    void start();

    void stop();

    void send(const char *data, int size);

    void sendAudio(const char *data, int size);

    void sendVideoData(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData,
                       int extraDataSize);

    void sendAudioData(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData,
                       int extraDataSize);

    bool isConnected();

    bool isVideoConnected();

    bool isAudioConnected();

    bool isRunning();

    IConnector *getVidConnector() { return mVideoConnector.get(); };

    IConnector *getAudConnector() { return mAudioConnector.get(); };

private:
    std::shared_ptr<IConnector> mVideoConnector;
    std::shared_ptr<IConnector> mAudioConnector;
    Callback *mCallback;
    OnVideoEventListener *mOnVideoEventListener;
    OnAudioEventListener *mOnAudioEventListener;
    bool mRunning;
};


#endif //SCREENCASTSOURCE_STREAMCONNECTOR_H
