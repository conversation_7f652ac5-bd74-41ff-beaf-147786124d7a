#ifndef SCREENCASTSOURCE_YVRNET_H
#define SCREENCASTSOURCE_YVRNET_H


#include <yvrnet_cpp.h>
#include <memory>
#include "IConnector.h"
#include <mutex>


class YVRNet : public IConnector, public yvrnet::IYvrnetCallback {
public:
    YVRNet();
    ~YVRNet();
    void start() override;
    void stop() override;
    void send(const char *data, int size) override;
    void send(const char *data, int size, int64_t timestamp) override;
    void send(const char *data, int size, int64_t timestamp, const char *extraData, int extraDataSize) override;
    void send(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize) override;
    void audioSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize) override;
    void videoSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize) override;

    void onEvent(const YvrnetEvent& event) override;
    void onConnectStatus(YvrnetConnectionStatus status) override;
    void onReceiveMessage(const char *message, int length) override;
    void onReceivePacket(const YvrnetMediaPacket *packet, int streamIndex) override;

private:
    YvrnetInstance mNet;
    std::mutex mMutex;
};


#endif //SCREENCASTSOURCE_YVRNET_H
