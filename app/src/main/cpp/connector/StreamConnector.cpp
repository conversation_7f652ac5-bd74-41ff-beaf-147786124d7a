#include "StreamConnector.h"
#include "YVRNet.h"
#include "Common.h"

StreamConnector::StreamConnector() {
    mCallback = nullptr;
    mRunning = false;
    mVideoConnector = std::make_shared<YVRNet>();
    mAudioConnector = std::make_shared<YVRNet>();

    mOnVideoEventListener = new OnVideoEventListener(this);
    mOnAudioEventListener = new OnAudioEventListener(this);

    mVideoConnector->setOnEventListener(mOnVideoEventListener);
    mAudioConnector->setOnEventListener(mOnAudioEventListener);
}

StreamConnector::~StreamConnector() {
    mVideoConnector->setOnEventListener(nullptr);
    mAudioConnector->setOnEventListener(nullptr);

    delete mOnVideoEventListener;
    delete mOnAudioEventListener;
}

void StreamConnector::configure(StreamConnector::Config &config) {
    mVideoConnector->setIp(config.videoIp);
    mVideoConnector->setPort(config.videoPort);
    mVideoConnector->setConnectionType(config.videoType);
    mVideoConnector->setConnectionMode(config.videoMode);

    mAudioConnector->setIp(config.audioIp);
    mAudioConnector->setPort(config.audioPort);
    mAudioConnector->setConnectionType(config.audioType);
    mAudioConnector->setConnectionMode(config.audioMode);
}

void StreamConnector::setCallback(StreamConnector::Callback *callback) {
    mCallback = callback;
}

void StreamConnector::start() {
    if (mRunning) {
        return;
    }
    mRunning = true;

    mVideoConnector->start();
    mAudioConnector->start();
}

void StreamConnector::stop() {
    if (!mRunning) {
        return;
    }
    mRunning = false;

    mVideoConnector->stop();
    mAudioConnector->stop();
}

bool StreamConnector::isConnected() {
    return mVideoConnector->isConnected() || mAudioConnector->isConnected();
}

bool StreamConnector::isVideoConnected() {
    return mVideoConnector->isConnected();
}

bool StreamConnector::isAudioConnected() {
    return mAudioConnector->isConnected();
}

bool StreamConnector::isRunning() {
    return mRunning;
}

void StreamConnector::send(const char *data, int size){
    mVideoConnector->send(data, size);
}

void StreamConnector::sendAudio(const char *data, int size){
    mAudioConnector->send(data, size);
}

void StreamConnector::sendVideoData(const char *data, int size, int64_t timestamp, int mediacodec,
                                    const char *extraData, int extraDataSize) {
    mVideoConnector->videoSend(data, size, timestamp, mediacodec, extraData, extraDataSize);
}

void StreamConnector::sendAudioData(const char *data, int size, int64_t timestamp, int mediacodec,
                                    const char *extraData, int extraDataSize) {
    mAudioConnector->audioSend(data, size, timestamp, mediacodec, extraData, extraDataSize);
}

void StreamConnector::OnVideoEventListener::onConnectionChanged(bool connected) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onVideoConnectionChanged(connected);
    }
}

void StreamConnector::OnVideoEventListener::onReceived(const char *data, int size) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onVideoReceived(data, size);
    }
}

void StreamConnector::OnVideoEventListener::onRequestKeyframe(bool enable) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onVideoRequestKeyframe(enable);
    }
}

void StreamConnector::OnAudioEventListener::onConnectionChanged(bool connected) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onAudioConnectionChanged(connected);
    }
}

void StreamConnector::OnAudioEventListener::onReceived(const char *data, int size) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onAudioReceived(data, size);
    }
}

void StreamConnector::OnAudioEventListener::onRequestKeyframe(bool enable) {
    if (mConnector->mCallback != nullptr) {
        mConnector->mCallback->onAudioRequestKeyframe(enable);
    }
}
