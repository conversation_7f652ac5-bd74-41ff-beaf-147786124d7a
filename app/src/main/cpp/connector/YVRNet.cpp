#include "YVRNet.h"
#include "Common.h"

YVRNet::YVRNet() {
    mNet = nullptr;
}

YVRNet::~YVRNet() = default;

void YVRNet::start() {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::start();
    YvrnetStartupConfig config;
    yvrnet_initStartupConfig(&config);
    config.role = getConnectionMode() == CONNECTION_MODE_CLIENT ? YVRNET_NR_CLIENT : YVRNET_NR_SERVER;
    config.type = YVRNET_DT_TCP;
//    config.config.yvrtc.videoCodec = YVRNET_VC_H264;
//    config.debug = true;
//    config.logLevel = YVRNET_LL_DEBUG;

    YvrnetErrorCode error;
    mNet = yvrnet_startup(&config, &error);
    if (error != YVRNET_EC_OK) {
        LOGE("Startup yvrnet fail: %s", yvrnet_errorToString(error));
        return;
    }
    yvrnet_getInstance(mNet)->addCallback(this);
    error = yvrnet_getInstance(mNet)->start(getIp().c_str(), std::stoi(getPort()));
    if (error != YVRNET_EC_OK) {
        LOGE("Start yvrnet fail: %s", yvrnet_errorToString(error));
        return;
    }

    LOGI("yvrnet started");
}

void YVRNet::stop() {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::stop();
    if (mNet != nullptr) {
        yvrnet_getInstance(mNet)->stop();
        yvrnet_getInstance(mNet)->removeCallback(this);
        yvrnet_shutdown(mNet);
        mNet = nullptr;
    }

    LOGI("yvrnet stopped");
}

void YVRNet::send(const char *data, int size) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size);
    if (mNet != nullptr) {
        yvrnet_getInstance(mNet)->sendMessage(reinterpret_cast<const char *>(data), size);
    }
}

void YVRNet::send(const char *data, int size, int64_t timestamp) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size, timestamp);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.timestamp = timestamp;
        packet.mediaType = YVRNET_MT_VIDEO;
        packet.codec.video = YVRNET_VC_H265;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::send(const char *data, int size, int64_t timestamp, const char *extraData, int extraDataSize) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size, timestamp, extraData, extraDataSize);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.timestamp = timestamp;
        packet.mediaType = YVRNET_MT_VIDEO;
        packet.codec.video = YVRNET_VC_H265;
        packet.extraData = (uint8_t *) extraData;
        packet.extraDataSize = extraDataSize;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::send(const char *data, int size, int64_t timestamp, int type, const char *extraData, int extraDataSize) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size, timestamp, extraData, extraDataSize);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.timestamp = timestamp;
        packet.mediaType = (YvrnetMediaType) type;
        if (type == YVRNET_MT_VIDEO) {
            packet.codec.video = YVRNET_VC_H265;
        } else {
            packet.codec.audio = YVRNET_AC_AAC;
        }

        packet.extraData = (uint8_t *) extraData;
        packet.extraDataSize = extraDataSize;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::audioSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size, timestamp, extraData, extraDataSize);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.timestamp = timestamp;
        packet.mediaType = YVRNET_MT_AUDIO;
        switch (mediacodec) {
            case MEDIACODEC_AUDIO_AAC:
                packet.codec.audio = YVRNET_AC_AAC;
                break;
            case MEDIACODEC_AUDIO_OPUS:
                packet.codec.audio = YVRNET_AC_OPUS;
                break;
            case MEDIACODEC_AUDIO_PCM:
                packet.codec.audio = YVRNET_AC_PCM;
                break;
            case MEDIACODEC_AUDIO_FLAC:
                packet.codec.audio = YVRNET_AC_FLAC;
                break;
            default:
                return;
        }

        packet.extraData = (uint8_t *) extraData;
        packet.extraDataSize = extraDataSize;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::videoSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize) {
    std::lock_guard<std::mutex> lock(mMutex);
    IConnector::send(data, size, timestamp, extraData, extraDataSize);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.timestamp = timestamp;
        packet.mediaType = YVRNET_MT_VIDEO;
        switch (mediacodec) {
            case MEDIACODEC_VIDEO_H265:
                packet.codec.video = YVRNET_VC_H265;
                break;
            case MEDIACODEC_VIDEO_H264:
                packet.codec.video = YVRNET_VC_H264;
                break;
            default:
                return;
        }

        packet.extraData = (uint8_t *) extraData;
        packet.extraDataSize = extraDataSize;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::onEvent(const YvrnetEvent &event) {
    LOGW("Net:onEvent: %d, ID: %d", (int) event.eventId, event.eventData.requireIdr.streamIndex);
    switch (event.eventId) {
        case YVRNET_EI_REQUIRE_IDR:
            setOnEventIDR(true);
            break;
        default:
            break;
    }
}

void YVRNet::onConnectStatus(YvrnetConnectionStatus status) {
    IYvrnetCallback::onConnectStatus(status);
    LOGW("[%s:%s]yvrnet: state: %d", mIp.c_str(), mPort.c_str(), (int) status);
    onConnectionChange(status == YVRNET_CS_CONNECTED);
}

void YVRNet::onReceiveMessage(const char *message, int length) {
    IYvrnetCallback::onReceiveMessage(message, length);
    onReceive(const_cast<char *>(message), length);
}

void YVRNet::onReceivePacket(const YvrnetMediaPacket *packet, int streamIndex) {
    IYvrnetCallback::onReceivePacket(packet, streamIndex);
}
