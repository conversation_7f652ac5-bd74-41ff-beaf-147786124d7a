#ifndef AUDIOTOOL_ICONNECTOR_H
#define AUDIOTOOL_ICONNECTOR_H

#include <cstdint>
#include <string>

#define TAG_CONNECTOR "PT-CONNECTOR"

typedef enum {
    CONNECTION_TYPE_TCP = 0,
    CONNECTION_TYPE_UDP,
    CONNECTION_TYPE_RTC,
    CONNECTION_TYPE_WIRED,
    CONNECTION_TYPE_WIRELESS,
    CONNECTION_TYPE_MAX
} connection_type_t;

typedef enum {
    CONNECTION_MODE_CLIENT = 0,
    CONNECTION_MODE_SERVER,
} connection_mode_t;

class OnEventListener {
public:
    virtual ~OnEventListener() = default;

    virtual void onConnectionChanged(bool connected) = 0;

    virtual void onRequestKeyframe(bool enable) = 0;

    virtual void onReceived(const char *data, int size) = 0;

    virtual void onReceived(const char *data, int size, int mediaType, int width, int height, void* pStatus) = 0;
};

class IConnector {

public:
    IConnector();
    virtual ~IConnector();
    virtual void setIp(std::string ip);
    virtual void setPort(std::string port);
    virtual std::string getIp();
    virtual std::string getPort();
    virtual void setConnectionType(connection_type_t type);
    virtual connection_type_t getConnectionType();
    virtual void setConnectionMode(connection_mode_t mode);
    virtual connection_mode_t getConnectionMode();
    virtual void start();
    virtual void stop();
    virtual bool isRunning();
    virtual bool isConnected();
    virtual void send(const char *data, int size);
    virtual void send(const char *data, int size, int64_t timestamp);
    virtual void send(const char *data, int size, int64_t timestamp, const char *extraData, int extraDataSize);
    virtual void send(const char *data, int size, int64_t timestamp, int type, const char *extraData, int extraDataSize);
    virtual void audioSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize);
    virtual void videoSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize);
    virtual void setOnEventListener(OnEventListener *listener);
    virtual OnEventListener *getOnEventListener();
    virtual void onConnectionChange(bool connected);
    virtual void onReceive(char *data, int size);
    virtual void setOnEventIDR(bool bResetIDR);

protected:
    std::string mIp;
    std::string mPort;
    bool mConnected;
    bool mRunning;
    connection_type_t mType;
    connection_mode_t mMode;
    OnEventListener *mListener = nullptr;
};


#endif //AUDIOTOOL_ICONNECTOR_H
