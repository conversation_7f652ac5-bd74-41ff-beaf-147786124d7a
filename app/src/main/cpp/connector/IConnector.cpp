#include "IConnector.h"


IConnector::IConnector() {
    mConnected = false;
    mRunning = false;
    mType = CONNECTION_TYPE_MAX;
    mMode = CONNECTION_MODE_SERVER;
}

IConnector::~IConnector() {
}

void IConnector::setIp(std::string ip) {
    mIp = ip;
}

void IConnector::setPort(std::string port) {
    mPort = port;
}

std::string IConnector::getIp() {
    return mIp;
}

std::string IConnector::getPort() {
    return mPort;
}

void IConnector::setConnectionType(connection_type_t type) {
    mType = type;
}

connection_type_t IConnector::getConnectionType() {
    return mType;
}

void IConnector::setConnectionMode(connection_mode_t mode) {
    mMode = mode;
}

connection_mode_t IConnector::getConnectionMode() {
    return mMode;
}

void IConnector::start() {
    mRunning = true;
}

void IConnector::stop() {
    mRunning = false;
    mConnected = false;
}

bool IConnector::isRunning() {
    return mRunning;
}

bool IConnector::isConnected() {
    return mConnected;
}

void IConnector::send(const char *data, int size) {

}

void IConnector::send(const char *data, int size, int64_t timestamp) {

}

void IConnector::send(const char *data, int size, int64_t timestamp, const char *extraData, int extraDataSize) {

}

void IConnector::send(const char *data, int size, int64_t timestamp, int type, const char *extraData, int extraDataSize) {

}

void IConnector::audioSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize){

}

void IConnector::videoSend(const char *data, int size, int64_t timestamp, int mediacodec, const char *extraData, int extraDataSize){
    
}

void IConnector::setOnEventListener(OnEventListener *listener) {
    mListener = listener;
}

OnEventListener *IConnector::getOnEventListener() {
    return mListener;
}

void IConnector::setOnEventIDR(bool bResetIDR){
    if (mListener != nullptr) {
        mListener->onRequestKeyframe(bResetIDR);
    }
}

void IConnector::onConnectionChange(bool connected) {
    if (mConnected == connected) {
        return;
    }

    mConnected = connected;

    if (mListener != nullptr) {
        mListener->onConnectionChanged(mConnected);
    }
}

void IConnector::onReceive(char *data, int size) {
    if (mListener != nullptr) {
        mListener->onReceived(data, size);
    }
}

void IConnector::onReceive(const char *data, int size, int mediaType, int width, int height, void *pStatus) {
}
