#ifndef SCREENCASTSOURCE_STREAMER_H
#define SCREENCASTSOURCE_STREAMER_H

#include "media/MediaCapture.h"
#include "connector/StreamConnector.h"
#include "utils/Statistics.hpp"
#include <media/NdkMediaMuxer.h>

typedef enum {
    STREAMER_FLAG_UNKNOWN = 0,
    STREAMER_FLAG_STREAMING_AUDIO = 1,
    STREAMER_FLAG_STREAMING_VIDEO = 1 << 1,
    STREAMER_FLAG_RECORD_MEDIA = 1 << 2,
    STREAMER_FLAG_RECORD_AUDIO = 1 << 3,
    STREAMER_FLAG_RECORD_VIDEO = 1 << 4,
} StreamerFlag;

class Streamer : MediaCapture::Callback, StreamConnector::Callback {
public:
    class Config {
    public:
        int id;
        Capture::Config captureConfig;
        StreamConnector::Config connectorConfig;
    };

    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onConnectionChanged(bool connected) = 0;
    };

public:
    class Recorder {
    public:
        Recorder(Streamer *streamer);

        ~Recorder();

        void start();

        void stop();

        bool isRunning() const;

        void videoBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo);

        void videoOutputFormatChanged(AMediaFormat *mediaFormat);

        void audioBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo);

        void audioOutputFormatChanged(AMediaFormat *mediaFormat);

        void setAudioOutputFile(std::string file);

        void setVideoOutputFile(std::string file);

        void setMediaOutputFile(std::string file);

    private:
        bool isMuxerReady() const;

        void resetMuxerTrack();

    private:
        Streamer *mStreamer;
        int mFD;
        std::atomic<long> mVideoTrackIndex;
        std::atomic<long> mAudioTrackIndex;
        mutable std::mutex mMutex;
        AMediaMuxer *mMediaMuxer;
        std::string mAudioFile;
        std::string mVideoFile;
        std::string mMediaFile;
        std::ofstream mAudioStream;
        std::ofstream mVideoStream;
        std::atomic<bool> mRunning;
    };

public:
    Streamer();

    ~Streamer() override;

    void configure(Streamer::Config &config);

    void start(StreamerFlag flag);

    void stop(StreamerFlag flag);

    bool isRunning() const;

    bool isConnected() const;

    void startRecording();

    void stopRecording();

    bool isRecording() const;

    void setFlag(int flag);

    bool hasFlag(int flag) const;

    void setCallback(Streamer::Callback *callback);

public:
    void onVideoBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

    void onVideoOutputFormatChanged(AMediaFormat *mediaFormat) override;

    void onAudioBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) override;

    void onAudioOutputFormatChanged(AMediaFormat *mediaFormat) override;

    void onVideoConnectionChanged(bool connected) override;

    void onVideoReceived(const char *data, int size) override;

    void onVideoRequestKeyframe(bool enable) override;

    void onAudioConnectionChanged(bool connected) override;

    void onAudioReceived(const char *data, int size) override;

    void onAudioRequestKeyframe(bool enable) override;

private:
    std::shared_ptr<MediaCapture> mMediaCapture;
    std::shared_ptr<StreamConnector> mStreamConnector;
    mutable std::mutex mMutex;
    Streamer::Config mConfig;
    int mAudIndex;
    int mVidIndex;
    std::shared_ptr<Streamer::Recorder> mRecorder;
    int mFlag;
    Streamer::Callback *mCallback;
    bool mConnected;
};


#endif //SCREENCASTSOURCE_STREAMER_H
