//
// Created by 122450 on 2024/9/26.
//
#include "Statistics.hpp"
#include "yvrnet_types.h"

Statistics Statistics::m_Instance;
int Statistic::m_PrintfLevel = 0;

// Statistic
Statistic::Statistic(int id, int mediaType) {
    mId = id;
    mMediaType = mediaType;
    ResetAll();
    mConnect = nullptr;
    mbStatisThread = true;
    mStatisThread = std::thread(&Statistic::Statics, this);
}

Statistic::~Statistic(){
    mbStatisThread = false;
    if (mStatisThread.joinable()) {
        mStatisThread.join();
    }
    PrintfTotal();
}

void Statistic::stop(){
    ResetAll();
    netConnect(false);
}

void Statistic::setNetDevice(IConnector* connector){
    mConnect = connector;
}

void Statistic::setMaxFrame(int frameLen) {
    if (m_PrintfLevel >= 0)
    {
        LOGI("[%d]%s set max frame: %d", mId, getMediaType(), frameLen);
    }

    m_u64MaxFrameLen = frameLen;
    return;
}

void Statistic::Statics()
{
    while (mbStatisThread)
    {
        CheckAndResetSecond();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void Statistic::ResetAll() {
    mStartCalc = false;
    std::lock_guard<std::mutex> lock(mStaticsMutex);
    m_current = 0;
    std::queue<int> frameId_empty;
    mFrameIdQueue.swap(frameId_empty);

    memset(&mtCap, 0, sizeof(FrameStatistic));
    memset(&mtEnc, 0, sizeof(FrameStatistic));
    memset(&mtSend, 0, sizeof(FrameStatistic));
    memset(&mtDec, 0, sizeof(FrameStatistic));
    memset(&mtPlay, 0, sizeof(FrameStatistic));
    memset(&mtTotal, 0, sizeof(FrameStatistic));
    memset(&mtMsgWork, 0, sizeof(NetStatistic));
    memset(&mtPckWork, 0, sizeof(NetStatistic));

    memset(&mtStatus, 0, sizeof(FrameStatus) * STATIS_MAX_NUM);
}

void Statistic::capFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64CapTime = latencyUs;
    mtStatus[framePosId].frameId = frameId;

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, size: %lu, cap: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, latencyUs);
    }

    CalcFrameInSecond(&mtCap, "cap", frameId, latencyUs, frameLen);
}

void Statistic::capFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0){
        return;
    }

    if (frameId < 0){
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64CapTime = timeUs;
}

void Statistic::capFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    int getFrameId = frameId;
    int framePosId = getFrameId % STATIS_MAX_NUM;
    uint64_t u64CapTime = 0;
    if (frameId >= 0){
        if (mtStatus[framePosId].u64CapTime == 0){
            return;
        }
        u64CapTime = mtStatus[framePosId].u64CapTime;
        latencyUs = latencyUs - u64CapTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64CapTime == timeUs; });
        if (it != std::end(mtStatus)) {
            u64CapTime = it->u64CapTime;
            latencyUs = latencyUs - it->u64CapTime;
            getFrameId = it->frameId;
        } else {
            if (m_PrintfLevel >= 255) {
                LOGW("[%d]%s capout:%d no cmp, size: %lu, enc: %ld, diff: %ld",
                     mId, getMediaType(), frameId, frameLen, timeUs, latencyUs);
            }
            return;
        }
    } else{
        return;
    }

    framePosId = getFrameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64CapTime = latencyUs;

    CalcFrameInSecond(&mtCap, "cap", getFrameId, latencyUs, frameLen);
    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, cap: %ld-%ld, diff: %ld",
             mId, getMediaType(), frameId,getFrameId, timeUs, u64CapTime, latencyUs);
    }
}

void Statistic::encFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (frameLen > m_u64MaxFrameLen){
        m_FrameOutRange++;
        m_FrameLastMax = std::max(m_FrameLastMax, frameLen);
    }

    if (m_PrintfLevel < 1){
        CalcFrameLostInSecond(&mtEnc, "enc", frameId);
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64EncTime = latencyUs;
    mtStatus[framePosId].dwEncLen = frameLen;
    CalcFrameInSecond( &mtEnc, "enc", frameId, latencyUs, frameLen);

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, size: %lu, enc: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, latencyUs);
    }

    if (mConnect){
        YvrnetStatus status = {0};
        status.sendID = mtStatus[framePosId].frameId;
        status.dwFrameLen = frameLen;
        status.u64SendTime = mtStatus[framePosId].u64SendTime;
        status.bVideoFrame = mMediaType == DECODER_AUDIO?false:true;
        CaleSendNet((const char *) &status, sizeof(YvrnetStatus));
    }
}

void Statistic::encFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    if (frameId < 0){
        return;
    }

    mtStatus[frameId % STATIS_MAX_NUM].u64EncTime = timeUs;
}

void Statistic::encFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (frameLen > m_u64MaxFrameLen){
        m_FrameOutRange++;
        m_FrameLastMax = std::max(m_FrameLastMax, frameLen);
    }

    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    int getFrameId = frameId;
    uint64_t u64EncTime = 0;
    int framePosId = getFrameId % STATIS_MAX_NUM;
    if (frameId >= 0){
        u64EncTime = mtStatus[framePosId].u64EncTime;
        if (u64EncTime == 0){
            return;
        }
        latencyUs = latencyUs - u64EncTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64EncTime == timeUs; });
        if (it != std::end(mtStatus)) {
            u64EncTime = it->u64EncTime;
            latencyUs = latencyUs - it->u64EncTime;
            getFrameId = it->frameId;
        } else {
            if (m_PrintfLevel >= 10) {
                LOGW("[%d]%s encout:%d no cmp, size: %lu, enc: %ld, diff: %ld",
                     mId, getMediaType(), frameId, frameLen, timeUs, latencyUs);
            }
            return;
        }
    }else{
        return;
    }

    framePosId = getFrameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64EncTime = latencyUs;
    mtStatus[framePosId].dwEncLen = frameLen;

    if (m_PrintfLevel >= 255) {
        LOGI("[%d]%s %d-%d, size: %lu, enc: %ld-%ld, diff: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, timeUs, u64EncTime, latencyUs);
    }

    CalcFrameInSecond(&mtEnc, "enc", getFrameId, latencyUs, frameLen);
}

void Statistic::sendFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }
    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64SendTime = latencyUs;

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, size: %lu, send: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, latencyUs);
    }

    CalcFrameInSecond( &mtSend, nullptr, frameId, latencyUs, frameLen);
}

void Statistic::sendFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    if (frameId < 0){
        return;
    }

    mtStatus[frameId % STATIS_MAX_NUM].u64SendTime = timeUs;
}

void Statistic::sendFramePro(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    int framePosId = frameId % STATIS_MAX_NUM;
    if (frameId >= 0){
        if (mtStatus[framePosId].u64SendTime == 0){
            return;
        }
        latencyUs = latencyUs - mtStatus[framePosId].u64SendTime;
    } else if ( timeUs > 0){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64SendTime == timeUs; });
        if (it != std::end(mtStatus)) {
            latencyUs = latencyUs - it->u64SendTime;
        } else {
            return;
        }
    }else{
        return;
    }

    mtPckWork.framesInSecond++;
    mtPckWork.latencyInSecond += latencyUs;
    mtPckWork.latencyMaxInSecond = std::max(mtPckWork.latencyMaxInSecond, latencyUs);

}

void Statistic::sendFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    uint64_t sendUs = 0;
    int getFrameId = frameId;
    int framePosId = getFrameId % STATIS_MAX_NUM;
    if (frameId >= 0){
        if (mtStatus[framePosId].u64SendTime == 0){
            return;
        }
        sendUs = mtStatus[framePosId].u64SendTime;
        latencyUs = latencyUs - mtStatus[framePosId].u64SendTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64SendTime == timeUs; });
        if (it != std::end(mtStatus)) {
            sendUs = it->u64SendTime;
            latencyUs = latencyUs - it->u64SendTime;
            getFrameId = it->frameId;
        } else {
            return;
        }
    }else{
        return;
    }

    framePosId = getFrameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64SendTime = latencyUs;

    if (m_PrintfLevel >= 255) {
        LOGI("[%d]%s %d-%d, size: %lu, send: %ld-%ld, diff: %ld",
             mId, getMediaType(), frameId, getFrameId, frameLen, timeUs, sendUs, latencyUs);
    }

    CalcFrameInSecond(&mtSend, nullptr, getFrameId, latencyUs, frameLen);
}

void Statistic::decFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64DecTime = latencyUs;

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, size: %lu, dec: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, latencyUs);
    }
    CalcFrameInSecond( &mtDec, nullptr, frameId, latencyUs, frameLen);
}

void Statistic::decFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    if (frameId < 0){
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64DecTime = timeUs;
}

int Statistic::decFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return -2;
    }

    if (mSecondTotal <= 0)
    {
        return -2;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    uint64_t decUs = 0;
    int getFrameId = frameId;
    int framePosId = getFrameId % STATIS_MAX_NUM;
    if (frameId >= 0){
        if (mtStatus[framePosId].u64DecTime == 0){
            return -2;
        }
        decUs = mtStatus[framePosId].u64DecTime;
        latencyUs = latencyUs - mtStatus[framePosId].u64DecTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64DecTime == timeUs; });
        if (it != std::end(mtStatus)) {
            decUs = it->u64DecTime;
            latencyUs = latencyUs - it->u64DecTime;
            getFrameId = it->frameId;
        } else {
            return -2;
        }
    }else{
        return -2;
    }

    framePosId = getFrameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64DecTime = latencyUs;

    if (m_PrintfLevel >= 255) {
        LOGI("[%d]%s %d-%d, size: %lu, dec: %ld-%ld, diff: %ld",
             mId, getMediaType(), frameId, getFrameId, frameLen, timeUs, decUs, latencyUs);
    }

    CalcFrameInSecond(&mtDec, "dec", getFrameId, latencyUs, frameLen);

    return getFrameId;
}

void Statistic::playFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64PlayTime = latencyUs;

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, size: %lu, play: %ld",
             mId, getMediaType(), frameId, framePosId, frameLen, latencyUs);
    }
    CalcFrameInSecond( &mtPlay, nullptr, frameId, latencyUs, frameLen);
}

void Statistic::playFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    if (frameId < 0){
        return;
    }

    mtStatus[frameId % STATIS_MAX_NUM].u64PlayTime = timeUs;
    mFrameIdQueue.push(frameId);
}

void Statistic::playFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    if (mFrameIdQueue.size() > 0)
    {
        mFrameIdQueue.pop();
    } else{
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    uint64_t playUs = 0;
    int getFrameId = frameId;
    if (frameId >= 0){
        playUs = mtStatus[frameId % STATIS_MAX_NUM].u64PlayTime;
        latencyUs = latencyUs - mtStatus[frameId % STATIS_MAX_NUM].u64PlayTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64PlayTime == timeUs; });
        if (it != std::end(mtStatus)) {
            playUs = it->u64PlayTime;
            latencyUs = latencyUs - it->u64PlayTime;
            getFrameId = it->frameId;
        } else {
            return;
        }
    }else{
        return;
    }

    int framePosId = getFrameId % STATIS_MAX_NUM;
    mtStatus[framePosId].u64PlayTime = latencyUs;

    CalcFrameInSecond(&mtPlay, "play", getFrameId, latencyUs, frameLen);

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d-%d, play: %ld-%ld, diff: %ld",
             mId, getMediaType(), frameId, framePosId, timeUs, playUs, latencyUs);
    }

    if (mConnect){
        YvrnetStatus status = {0};
        status.sendID = mtStatus[framePosId].frameId;
        status.dwFrameLen = mtStatus[framePosId].dwEncLen;
        status.u64FrameBeginTime = mtStatus[framePosId].u64TotalTime;
        status.u64CapTime = mtStatus[framePosId].u64CapTime;
        status.u64SendTime = mtStatus[framePosId].u64SendTime;
        status.u64EncTime = mtStatus[framePosId].u64EncTime;
        status.u64DecTime = mtStatus[framePosId].u64DecTime;
        status.u64InterTime = mtStatus[framePosId].u64PlayTime;
        status.bVideoFrame = mMediaType == DECODER_AUDIO?false:true;
        CaleSendNet((const char *) &status, sizeof(YvrnetStatus));
    }
}

void Statistic::playFrameOut(bool bdiscardFrame) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    int getFrameId = 0;
    if (mFrameIdQueue.size() > 0)
    {
        getFrameId = mFrameIdQueue.front();
        mFrameIdQueue.pop();
    } else{
        return;
    }

    if (bdiscardFrame){
        mtPlay.frameIndex = getFrameId;
        return;
    }

    int framePosId = getFrameId % STATIS_MAX_NUM;
    uint64_t latencyUs = getCurrentTimestampUs() - mtStatus[framePosId].u64PlayTime;
    mtStatus[framePosId].u64PlayTime = latencyUs;

    CalcFrameInSecond(&mtPlay, "play", getFrameId, latencyUs, 0);

    if (m_PrintfLevel >= 255)
    {
        LOGI("[%d]%s %d, playdis: %ld, size: %zu", mId, getMediaType(), getFrameId, latencyUs, mFrameIdQueue.size());
    }

    if (mConnect){
        YvrnetStatus status = {0};
        status.capID = mtStatus[framePosId].frameId;
        status.sendID = mtStatus[framePosId].frameId;
        status.dwFrameLen = mtStatus[framePosId].dwEncLen;
        status.streamID = mId;
        status.u64FrameBeginTime = mtStatus[framePosId].u64TotalTime;
        status.u64CapTime = mtStatus[framePosId].u64CapTime;
        status.u64SendTime = mtStatus[framePosId].u64SendTime;
        status.u64EncTime = mtStatus[framePosId].u64EncTime;
        status.u64DecTime = mtStatus[framePosId].u64DecTime;
        status.u64InterTime = mtStatus[framePosId].u64PlayTime;
        status.bVideoFrame = mMediaType == DECODER_AUDIO?false:true;
        CaleSendNet((const char *) &status, sizeof(YvrnetStatus));
    }
}

void Statistic::totalFrame(int frameId, uint64_t latencyUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    mStartCalc = true;
    if (mSecondTotal <= 0)
    {
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    if (Statistics::Instance().enablePrintf(10)) {
        LOGI("[%d]%s id %d; time(cap: %lu, enc: %lu, send: %lu, dec: %lu, play: %lu, total: %lu)", mId,getMediaType(),
             frameId, mtStatus[framePosId].u64CapTime, mtStatus[framePosId].u64EncTime,
             mtStatus[framePosId].u64SendTime, mtStatus[framePosId].u64DecTime,
             mtStatus[framePosId].u64PlayTime, latencyUs);
    }

    mtStatus[framePosId].u64TotalTime = latencyUs;

    CalcFrameInSecond( &mtTotal, nullptr, frameId, latencyUs, frameLen);
}

void Statistic::totalFrameIn(int frameId, uint64_t timeUs) {
    if (m_PrintfLevel < 1){
        return;
    }

    mStartCalc = true;
    if (mSecondTotal <= 0)
    {
        return;
    }

    if (frameId < 0){
        return;
    }

    int framePosId = frameId % STATIS_MAX_NUM;
    memset(&mtStatus[framePosId], 0, sizeof(FrameStatus) );
    mtStatus[framePosId].frameId = frameId;
    mtStatus[framePosId].u64TotalTime = timeUs;
}

void Statistic::totalFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen) {
    if (m_PrintfLevel < 1){
        return;
    }

    if (mSecondTotal <= 0)
    {
        return;
    }

    uint64_t latencyUs = getCurrentTimestampUs();
    int getFrameId = frameId;
    int framePosId = getFrameId % STATIS_MAX_NUM;
    if (frameId >= 0){
        if (mtStatus[framePosId].u64TotalTime == 0){
            return;
        }
        latencyUs = latencyUs - mtStatus[framePosId].u64TotalTime;
    } else if ( timeUs > 0 && frameId == STATIS_INVALID_ID){
        auto it = std::find_if(std::begin(mtStatus), std::end(mtStatus),
                               [timeUs](const FrameStatus& fs) { return fs.u64TotalTime == timeUs; });
        if (it != std::end(mtStatus)) {
            latencyUs = latencyUs - it->u64TotalTime;
            getFrameId = it->frameId;
        } else {
            if (m_PrintfLevel >= 255) {
                LOGW("[%d]%s totalout:%d no cmp, size: %lu, enc: %ld, diff: %ld",
                     mId, getMediaType(), frameId, frameLen, timeUs, latencyUs);
            }
            return;
        }
    } else{
        return;
    }

    framePosId = getFrameId % STATIS_MAX_NUM;
    if (m_PrintfLevel >= 10) {
        LOGI("[%d]%s total size %d, id: %d, in: %lu, cap: %lu, enc: %lu, net: %lu, dec: %lu, play: %lu, all: %lu",
             mId, getMediaType(),mtStatus[framePosId].dwEncLen, frameId,
             mtStatus[framePosId].u64TotalTime, mtStatus[framePosId].u64CapTime,
             mtStatus[framePosId].u64EncTime,
             mtStatus[framePosId].u64SendTime,
             mtStatus[framePosId].u64DecTime,
             mtStatus[framePosId].u64PlayTime,
             latencyUs);
    }
    mtStatus[framePosId].u64TotalTime = latencyUs;

    CalcFrameInSecond(&mtTotal, nullptr, getFrameId, latencyUs, frameLen);

    if (mConnect){
        YvrnetStatus status = {0};
        status.capID = mtStatus[framePosId].frameId;
        status.sendID = mtStatus[framePosId].frameId;
        status.streamID = mId;
        status.dwFrameLen = mtStatus[framePosId].dwEncLen;
        status.u64FrameBeginTime = mtStatus[framePosId].u64TotalTime;
        status.u64CapTime = mtStatus[framePosId].u64CapTime;
        status.u64SendTime = mtStatus[framePosId].u64SendTime;
        status.u64EncTime = mtStatus[framePosId].u64EncTime;
        status.u64DecTime = mtStatus[framePosId].u64DecTime;
        status.u64InterTime = mtStatus[framePosId].u64PlayTime;
        status.bVideoFrame = mMediaType == DECODER_AUDIO?false:true;
        CaleSendNet((const char *) &status, sizeof(YvrnetStatus));
    }
}

void Statistic::getYvrnetStatus( int frameId, YvrnetStatus* netStatus){
    if (netStatus == nullptr){
        return;
    }
    int framePosid = frameId % STATIS_MAX_NUM;

    netStatus->sendID = frameId;
    netStatus->bVideoFrame = mMediaType == DECODER_AUDIO?false:true;

    netStatus->capID = mtStatus[framePosid].frameId;
    netStatus->streamID = mId;
    netStatus->u64FrameBeginTime = mtStatus[framePosid].u64TotalTime;
    netStatus->u64CapTime = mtStatus[framePosid].u64CapTime;
    netStatus->u64EncTime = mtStatus[framePosid].u64EncTime;
    netStatus->u64SendTime = mtStatus[framePosid].u64SendTime;
    netStatus->u64DecTime = mtStatus[framePosid].u64DecTime;
    netStatus->u64InterTime = mtStatus[framePosid].u64PlayTime;
    netStatus->dwFrameLen = mtStatus[framePosid].dwEncLen;

    return;
}

uint32_t Statistic::GetFrameInSecond(FrameStatistic* pFrame) {
    return pFrame->framesInSecondPrev;
}

float Statistic::GetLatencyInSecond(FrameStatistic* pFrame){
    if (pFrame->framesInSecondPrev == 0){
        return 0.0f;
    }
    return (float)pFrame->latencyTotalInSecondPrev / pFrame->framesInSecondPrev;
}

uint32_t Statistic::GetMaxLatencyInSecond(FrameStatistic* pFrame){
    return pFrame->latencyMaxInSecondPrev;
}

uint32_t Statistic::GetFrameLostInSecond(FrameStatistic* pFrame){
    return pFrame->framesLostInSecondPrev;
}

float Statistic::GetBitrateInSecond(FrameStatistic* pFrame){
    return (float)(pFrame->frameLengthInSecondPrev << 3) /1048576;
}

uint32_t Statistic::GetFrameLost(FrameStatistic* pFrame){
    return pFrame->framesLostTotal;
}

uint32_t Statistic::GetFrameTotal(FrameStatistic* pFrame){
    return pFrame->framesTotal;
}

bool Statistic::enablePrintf(int level){
    return m_PrintfLevel >= level;
}

const char* Statistic::getMediaType(){
    return mMediaType==DECODER_AUDIO?"aud":"vid";
}

void Statistic::PrintfTotal()
{
    LOGI("[%d]%s total cap: %d / lost: %d, enc: %d / lost: %d, send %d / lost: %d, dec: %d / lost: %d, play: %d / lost: %d, total: %d / lost: %d",
         mId, getMediaType(),
         GetFrameTotal(&mtCap), GetFrameLost(&mtCap),
         GetFrameTotal(&mtEnc), GetFrameLost(&mtEnc),
         GetFrameTotal(&mtSend), GetFrameLost(&mtSend),
         GetFrameTotal(&mtDec), GetFrameLost(&mtDec),
         GetFrameTotal(&mtPlay), GetFrameLost(&mtPlay),
         GetFrameTotal(&mtTotal), GetFrameLost(&mtTotal));
}

void Statistic::netConnect(bool connect){
    mbConnect = connect;
}

bool Statistic::setSaveFile(bool bsave){
    mbSaveFile = bsave;
    return true;
}

bool Statistic::saveFile(){
    return mbSaveFile;
}

void Statistic::ResetSecond() {
    ResetFrameInSecond(&mtCap);
    ResetFrameInSecond(&mtEnc);
    ResetFrameInSecond(&mtSend);
    ResetFrameInSecond(&mtDec);
    ResetFrameInSecond(&mtPlay);
    ResetFrameInSecond(&mtTotal);

    ResetNetInSecond(&mtMsgWork);
    ResetNetInSecond(&mtPckWork);
}

void Statistic::ResetNetInSecond(NetStatistic* pFrame){
    pFrame->framesInSecondPre = pFrame->framesInSecond;
    pFrame->framesInSecond = 0;
    pFrame->latencyInSecondPrev = pFrame->latencyInSecond;
    pFrame->latencyInSecond = 0;
    pFrame->latencyMaxInSecondPrev = pFrame->latencyMaxInSecond;
    pFrame->latencyMaxInSecond = 0;
}

uint32_t Statistic::GetNetInSecond(NetStatistic* pFrame){
    return pFrame->framesInSecondPre;
}

float Statistic::GetNetLatencyInSecond(NetStatistic* pFrame){
    if (pFrame->latencyInSecondPrev == 0){
        return 0.0f;
    }
    return (float)pFrame->latencyInSecondPrev / pFrame->framesInSecondPre;
}

uint32_t Statistic::GetNetMaxLatencyInSecond( NetStatistic* pFrame){
    return pFrame->latencyMaxInSecondPrev;
}

void *Statistic::CheckAndResetSecond() {
    auto duration = std::chrono::system_clock::now().time_since_epoch();
    uint64_t current = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count()/1000;
    if (0 == m_current)
    {
        m_current = current;
        mSecondTotal = -1;
        return nullptr;
    }

    if (m_current != current) {
        m_current = current;
        ResetSecond();

        if (mSecondTotal > 0)
        {
            if (mStartCalc && Statistic::m_PrintfLevel >= 1){
                LOGI("[%d]%s 1s, cap: %.2f[ %d ] / max: %u, enc: %.2f[ %d ] / max: %u, bitrate: %.2fM, send %s: %.2f[ %d ] / max: %u, lost: %d, pkg: %.2f[ %d ]/ max: %u, msg: %.2f[ %d ]/ max: %u, dec: %.2f[ %d ]/ max: %u, play: %.2f[ %d ]/ max: %u, total: %.2f / max: %u",
                     mId, getMediaType(),
                     GetLatencyInSecond(&mtCap)/1000, GetFrameInSecond(&mtCap), GetMaxLatencyInSecond(&mtCap)/1000,
                     GetLatencyInSecond(&mtEnc)/1000, GetFrameInSecond(&mtEnc), GetMaxLatencyInSecond(&mtEnc)/1000,
                     GetBitrateInSecond(&mtEnc),mbConnect == true?"connect":"connecting",
                     GetLatencyInSecond(&mtSend)/1000, GetFrameInSecond(&mtSend), GetMaxLatencyInSecond(&mtSend)/1000,
                     GetFrameLostInSecond(&mtSend),
                     GetNetLatencyInSecond(&mtPckWork)/1000, GetNetInSecond(&mtPckWork), GetNetMaxLatencyInSecond(&mtPckWork)/1000,
                     GetNetLatencyInSecond(&mtMsgWork), GetNetInSecond(&mtMsgWork), GetNetMaxLatencyInSecond(&mtMsgWork),
                     GetLatencyInSecond(&mtDec)/1000, GetFrameInSecond(&mtDec), GetMaxLatencyInSecond(&mtDec)/1000,
                     GetLatencyInSecond(&mtPlay)/1000, GetFrameInSecond(&mtPlay), GetMaxLatencyInSecond(&mtPlay)/1000,
                     GetLatencyInSecond(&mtTotal)/1000, GetMaxLatencyInSecond(&mtTotal)/1000);
            }

            if ( m_FrameOutRange > 0){
                LOGI("[%d]%s 1s, large: %d / %lu", mId, getMediaType(),
                     m_FrameOutRange, m_FrameLastMax);
            }
        }

        mSecondTotal++;

        return nullptr;
    }else
    {
        return nullptr;
    }
}

void Statistic::ResetFrameInSecond(FrameStatistic* pFrame){
    std::lock_guard<std::mutex> lock(mStaticsMutex);
    pFrame->framesInSecondPrev = pFrame->framesInSecond;
    pFrame->framesInSecond = 0;
    pFrame->latencyMaxInSecondPrev = pFrame->latencyMaxInSecond;
    pFrame->latencyMaxInSecond = 0;
    pFrame->latencyTotalInSecondPrev = pFrame->latencyTotalInSecond;
    pFrame->latencyTotalInSecond = 0;
    pFrame->framesLostInSecondPrev = pFrame->framesLostInSecond;
    pFrame->framesLostInSecond = 0;
    pFrame->frameLengthInSecondPrev = pFrame->frameLengthInSecond;
    pFrame->frameLengthInSecond = 0;
}

void Statistic::CalcFrameInSecond(FrameStatistic* pFrame, const char* ptr, int frameId, uint64_t latencyUs, uint64_t frameLen){
    std::lock_guard<std::mutex> lock(mStaticsMutex);
    pFrame->framesInSecond++;
    pFrame->framesTotal++;

    pFrame->latencyTotalInSecond += latencyUs;
    pFrame->latencyMaxInSecond = std::max(latencyUs, pFrame->latencyMaxInSecond);

    if ( ptr && pFrame->frameIndex != 0 && frameId != 0){
        if (pFrame->frameIndex == 0x7fffffff){
            pFrame->frameIndex = 0;
        } else if (pFrame->frameIndex + 1 != frameId){
            pFrame->framesLostInSecond++;
            pFrame->framesLostTotal++;
            LOGW("[%d]%s %s lost id:%d, last id:%d", mId, getMediaType(), ptr, frameId, pFrame->frameIndex);
        }
    }
    pFrame->frameIndex = frameId;

    if (frameLen != 0){
        pFrame->frameLengthInSecond += frameLen;
    }
}

void Statistic::CalcFrameLostInSecond(FrameStatistic* pFrame, const char* ptr, int frameId){
    std::lock_guard<std::mutex> lock(mStaticsMutex);
    if (ptr && pFrame->frameIndex != 0 && frameId != 0){
        if (pFrame->frameIndex == 0x7fffffff){
            pFrame->frameIndex = 0;
        } else if (pFrame->frameIndex + 1 != frameId){
            pFrame->framesLostInSecond++;
            pFrame->framesLostTotal++;
            LOGW("[%d]%s %s lost id:%d, last id:%d", mId, getMediaType(), ptr, frameId, pFrame->frameIndex);
            if (mConnect && mMediaType==DECODER_VIDEO){
                YvrnetEvent event = {YVRNET_EI_REQUIRE_IDR, 0};
                mConnect->send((const char *) &event, sizeof(YvrnetEvent));
            }
        }
    }
    pFrame->frameIndex = frameId;
}

void Statistic::CaleSendNet(const char * ptr, int length){
    uint64_t timeget = getCurrentTimeMs();
    mConnect->send(ptr, length);
    timeget = getCurrentTimeMs() - timeget;
    mtMsgWork.framesInSecond++;
    mtMsgWork.latencyInSecond += timeget;
    mtMsgWork.latencyMaxInSecond = std::max(mtMsgWork.latencyMaxInSecond, timeget);
}

// Statistics
Statistics::Statistics() {
    for (int i = 0; i < DEVICE_ID_MAX; ++i) {
        mVidStatis[i] = std::make_shared<Statistic>(i, DECODER_VIDEO);
        mAudStatis[i] = std::make_shared<Statistic>(i, DECODER_AUDIO);
    }
}

Statistics::~Statistics() {
    for (int i = 0; i < DEVICE_ID_MAX; ++i) {
        mVidStatis[i].reset();
        mAudStatis[i].reset();
    }
}

void Statistics::stop(int id, int mediaType){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->stop();
    }else{
        mVidStatis[id]->stop();
    }
}

void Statistics::setMaxFrame(int id, int mediaType, int frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->setMaxFrame(frameLen);
    }else{
        mVidStatis[id]->setMaxFrame(frameLen);
    }
}

void Statistics::setNetDevice(int id, int mediaType, IConnector* connector){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->setNetDevice(connector);
    }else{
        mVidStatis[id]->setNetDevice(connector);
    }
}

void Statistics::capFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->capFrame(frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->capFrame(frameId, latencyUs, frameLen);
    }
}

void Statistics::capFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->capFrameIn(frameId, timeUs);
    }else{
        mVidStatis[id]->capFrameIn(frameId, timeUs);
    }
}

void Statistics::capFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->capFrameOut(frameId, timeUs, frameLen);
    }else{
        mVidStatis[id]->capFrameOut(frameId, timeUs, frameLen);
    }
}

void Statistics::encFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->encFrame( frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->encFrame( frameId, latencyUs, frameLen);
    }
}

void Statistics::encFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->encFrameIn( frameId, timeUs);
    }else{
        mVidStatis[id]->encFrameIn( frameId, timeUs);
    }
}

void Statistics::encFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->encFrameOut( frameId, timeUs, frameLen);
    }else{
        mVidStatis[id]->encFrameOut( frameId, timeUs, frameLen);
    }
}

void Statistics::sendFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->sendFrame(frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->sendFrame(frameId, latencyUs, frameLen);
    }
}

void Statistics::sendFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->sendFrameIn( frameId, timeUs);
    }else{
        mVidStatis[id]->sendFrameIn( frameId, timeUs);
    }
}

void Statistics::sendFramePro(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->sendFramePro( frameId, timeUs, frameLen);
    }else{
        mVidStatis[id]->sendFramePro( frameId, timeUs, frameLen);
    }
}

void Statistics::sendFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->sendFrameOut( frameId, timeUs, frameLen);
    }else{
        mVidStatis[id]->sendFrameOut( frameId, timeUs, frameLen);
    }
}

void Statistics::decFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->decFrame( frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->decFrame( frameId, latencyUs, frameLen);
    }
}

void Statistics::decFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->decFrameIn( frameId, timeUs);
    }else{
        mVidStatis[id]->decFrameIn( frameId, timeUs);
    }
}

int Statistics::decFrameOut(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return -2;
    }

    if (DECODER_AUDIO == mediaType)
    {
        return mAudStatis[id]->decFrameOut( frameId, latencyUs, frameLen);
    }else{
        return mVidStatis[id]->decFrameOut( frameId, latencyUs, frameLen);
    }
}

void Statistics::playFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->playFrame( frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->playFrame( frameId, latencyUs, frameLen);
    }
}

void Statistics::playFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->playFrameIn( frameId, timeUs);
    }else{
        mVidStatis[id]->playFrameIn( frameId, timeUs);
    }
}

void Statistics::playFrameOut(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->playFrameOut( frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->playFrameOut( frameId, latencyUs, frameLen);
    }
}

void Statistics::playFrameOut(int id, int mediaType, bool bdiscardFrame){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->playFrameOut( bdiscardFrame);
    }else{
        mVidStatis[id]->playFrameOut( bdiscardFrame);
    }
}

void Statistics::totalFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->totalFrame( frameId, latencyUs, frameLen);
    }else{
        mVidStatis[id]->totalFrame( frameId, latencyUs, frameLen);
    }
}

void Statistics::totalFrameIn(int id, int mediaType, int frameId, uint64_t timeUs){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->totalFrameIn( frameId, timeUs);
    }else{
        mVidStatis[id]->totalFrameIn( frameId, timeUs);
    }
}

void Statistics::totalFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->totalFrameOut( frameId, timeUs, frameLen);
    }else{
        mVidStatis[id]->totalFrameOut( frameId, timeUs, frameLen);
    }
}

void Statistics::getYvrnetStatus(int id, int mediaType, int frameId, YvrnetStatus* netStatus){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->getYvrnetStatus( frameId, netStatus);
    }else{
        mVidStatis[id]->getYvrnetStatus( frameId, netStatus);
    }
    return;
}

void Statistics::netConnect(int id, int mediaType, bool connect){
    if (id >= DEVICE_ID_MAX)
    {
        return;
    }

    if (DECODER_AUDIO == mediaType)
    {
        mAudStatis[id]->netConnect(connect);
    }else{
        mVidStatis[id]->netConnect(connect);
    }
}

void Statistics::PrintfLevel( int level){
    LOGW("printf level: %d", level);
    Statistic::m_PrintfLevel = level;
}

bool Statistics::enablePrintf(int level){
    return Statistic::m_PrintfLevel >= level;
}

bool Statistics::setSaveFile(int id, int mediatype, bool bsave){
    if (id >= DEVICE_ID_MAX)
    {
        return false;
    }

    if (DECODER_AUDIO == mediatype)
    {
        LOGW("[%d]save audio dat file: %d",id, bsave);
        return mAudStatis[id]->setSaveFile(bsave);
    }else{
        LOGW("[%d]save video dat file: %d",id, bsave);
        return mVidStatis[id]->setSaveFile(bsave);
    }
}

bool Statistics::saveFile(int id, int mediatype){
    if (id >= DEVICE_ID_MAX)
    {
        return false;
    }

    if (DECODER_AUDIO == mediatype)
    {
        return mAudStatis[id]->saveFile();
    }else{
        return mVidStatis[id]->saveFile();
    }
}