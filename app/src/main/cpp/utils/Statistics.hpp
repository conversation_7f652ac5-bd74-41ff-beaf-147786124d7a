#pragma once

#include <algorithm>
#include <stdint.h>
#include <thread>
#include <queue>
#include "Common.h"
#include "../connector/IConnector.h"

#define DECODER_AUDIO 0
#define DECODER_VIDEO 1
#define DEVICE_ID_MAX 2
#define STATIS_MAX_NUM 1000
#define STATIS_INVALID_ID -1

struct FrameStatistic{
    uint32_t framesTotal = 0;
    uint32_t framesInSecond = 0;
    uint32_t framesInSecondPrev = 0;

    uint64_t latencyTotalInSecond = 0;
    uint64_t latencyTotalInSecondPrev = 0;

    uint64_t latencyMaxInSecond = 0;
    uint64_t latencyMaxInSecondPrev = 0;

    uint32_t framesLostTotal = 0;
    uint32_t framesLostInSecond = 0;
    uint32_t framesLostInSecondPrev = 0;

    int frameIndex = 0;

    uint64_t frameLengthInSecond = 0;
    uint64_t frameLengthInSecondPrev = 0;
};

struct NetStatistic{
    uint32_t framesInSecond = 0;
    uint32_t framesInSecondPre = 0;
    uint64_t latencyInSecond = 0;
    uint64_t latencyInSecondPrev = 0;
    uint64_t latencyMaxInSecond = 0;
    uint64_t latencyMaxInSecondPrev = 0;
};

typedef struct FrameStatus {
    int frameId;
    uint64_t u64TotalTime;
    uint64_t u64CapTime;
    uint64_t u64EncTime;
    uint64_t u64SendTime;
    uint64_t u64DecTime;
    uint64_t u64PlayTime;
    int dwEncLen;
} FrameStatus;

inline int64_t getCurrentTimeUs(){
    auto duration = std::chrono::system_clock::now().time_since_epoch();
    auto current = std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    return current;
}

inline int64_t getCurrentTimeMs(){
    auto duration = std::chrono::system_clock::now().time_since_epoch();
    auto current = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    return current;
}

class Statistic {
public:
	Statistic(int id, int mediaType);
    ~Statistic();
    void stop();
    void setNetDevice(IConnector* connector);
    void capFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void capFrameIn(int frameId, uint64_t timeUs);
    void capFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void encFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void encFrameIn(int frameId, uint64_t timeUs);
    void encFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void sendFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void sendFrameIn(int frameId, uint64_t timeUs);
    void sendFramePro(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void sendFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void decFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void decFrameIn(int frameId, uint64_t timeUs);
    int decFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void playFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void playFrameIn(int frameId, uint64_t timeUs);
    void playFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void playFrameOut(bool bdiscardFrame);
    void totalFrame(int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void totalFrameIn(int frameId, uint64_t timeUs);
    void totalFrameOut(int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void getYvrnetStatus( int frameId, YvrnetStatus* netStatus);
    void setMaxFrame(int frameLen);
    bool enablePrintf(int level);
    void netConnect(bool connect);
    bool setSaveFile(bool bsave);
    bool saveFile();

private:
    void ResetAll();
    void Statics();
    uint32_t GetFrameInSecond(FrameStatistic* pFrame);
    float GetLatencyInSecond(FrameStatistic* pFrame);
    uint32_t GetMaxLatencyInSecond(FrameStatistic* pFrame);
    uint32_t GetFrameLostInSecond(FrameStatistic* pFrame);
    float GetBitrateInSecond(FrameStatistic* pFrame);
    uint32_t GetFrameLost(FrameStatistic* pFrame);
    uint32_t GetFrameTotal(FrameStatistic* pFrame);

	void PrintfTotal();
    void ResetSecond();
    void *CheckAndResetSecond();
    void ResetFrameInSecond(FrameStatistic* pFrame);
    void CalcFrameInSecond( FrameStatistic* pFrame, const char* ptr,int frameId, uint64_t latencyUs, uint64_t frameLen);
    void CalcFrameLostInSecond( FrameStatistic* pFrame, const char* ptr, int frameId);

    void CaleSendNet(const char * ptr, int length);
    void ResetNetInSecond(NetStatistic* pFrame);
    uint32_t GetNetInSecond(NetStatistic* pFrame);
    float GetNetLatencyInSecond(NetStatistic* pFrame);
    uint32_t GetNetMaxLatencyInSecond( NetStatistic* pFrame);

    const char* getMediaType();

public:
    static int m_PrintfLevel;

private:
	bool mbSaveFile = false;
	std::mutex mStaticsMutex;
    IConnector* mConnect;

    FrameStatistic mtCap;
    FrameStatistic mtEnc;
    FrameStatistic mtSend;
    FrameStatistic mtDec;
    FrameStatistic mtPlay;
    FrameStatistic mtTotal;
    FrameStatus mtStatus[STATIS_MAX_NUM] = {0};
    NetStatistic mtMsgWork;
    NetStatistic mtPckWork;
    std::queue<int> mFrameIdQueue;

    uint64_t m_u64MaxFrameLen = 0;
    int m_FrameOutRange = 0;
    uint64_t m_FrameLastMax = 0;

	uint64_t m_current = 0;
	int mId = 0;
	int mSecondTotal = 0;
    bool mbConnect = false;
	int mMediaType = DECODER_AUDIO;
    bool mStartCalc = false;

    bool        mbStatisThread = 0;
    std::thread mStatisThread;
};

class Statistics
{
	static Statistics m_Instance;

	Statistics();
	virtual ~Statistics();

	std::shared_ptr<Statistic> mVidStatis[DEVICE_ID_MAX];
	std::shared_ptr<Statistic> mAudStatis[DEVICE_ID_MAX];

public:
	static Statistics &Instance() {
		return m_Instance;
	}

	void stop(int id, int mediaType);
	void setMaxFrame(int id, int mediaType, int frameLen);
    void setNetDevice(int id, int mediaType, IConnector* connector);
    void capFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void capFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    void capFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void encFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void encFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    void encFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void sendFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void sendFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    void sendFramePro(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void sendFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void decFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void decFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    int decFrameOut(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void playFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void playFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    void playFrameOut(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void playFrameOut(int id, int mediaType, bool bdiscardFrame);
    void totalFrame(int id, int mediaType, int frameId, uint64_t latencyUs, uint64_t frameLen = 0);
    void totalFrameIn(int id, int mediaType, int frameId, uint64_t timeUs);
    void totalFrameOut(int id, int mediaType, int frameId, uint64_t timeUs, uint64_t frameLen = 0);
    void getYvrnetStatus(int id, int mediaType, int frameId, YvrnetStatus* netStatus);
    void PrintfLevel( int level);
    bool enablePrintf(int level);
    void netConnect(int id, int mediaType, bool connect);
    bool setSaveFile(int id, int mediaType, bool bsave);
    bool saveFile(int id, int mediatype);
};
