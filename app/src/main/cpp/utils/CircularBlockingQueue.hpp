#ifndef CIRCULARBLOCKINGQUEUE_H
#define CIRCULARBLOCKINGQUEUE_H

#include <iostream>
#include <array>
#include <mutex>
#include <condition_variable>
#include <stdexcept>

template<typename T, std::size_t N>
class CircularBlockingQueue {
public:
    CircularBlockingQueue() : head(0), tail(0), count(0) {
        if (N == 0) {
            throw std::invalid_argument("Queue size must be greater than 0");
        }
    }

    void push(const T &item) {
        std::unique_lock<std::mutex> lock(mtx);
        not_full.wait(lock, [this]() { return count < N; });

        buffer[tail] = item;
        tail = (tail + 1) % N;
        ++count;

        not_empty.notify_one();
    }

    T pop() {
        std::unique_lock<std::mutex> lock(mtx);
        not_empty.wait(lock, [this]() { return count > 0; });

        T item = buffer[head];
        head = (head + 1) % N;
        --count;

        not_full.notify_one();
        return item;
    }

    bool tryPush(const T &item) {
        std::unique_lock<std::mutex> lock(mtx);
        if (count >= N) {
            return false; // Queue is full
        }

        buffer[tail] = item;
        tail = (tail + 1) % N;
        ++count;

        not_empty.notify_one();
        return true;
    }

    bool tryPop(T &item) {
        std::unique_lock<std::mutex> lock(mtx);
        if (count <= 0) {
            return false; // Queue is empty
        }

        item = buffer[head];
        head = (head + 1) % N;
        --count;

        not_full.notify_one();
        return true;
    }

    std::size_t size() const {
        std::unique_lock<std::mutex> lock(mtx);
        return count;
    }

    bool empty() const {
        return size() == 0;
    }

    bool full() const {
        return size() == N;
    }

    void clear() {
        std::unique_lock<std::mutex> lock(mtx);
        head = 0;
        tail = 0;
        count = 0;

        // Notify all waiting threads to avoid potential deadlocks
        not_empty.notify_all();
        not_full.notify_all();
    }

private:
    std::array<T, N> buffer;
    std::size_t head;
    std::size_t tail;
    std::size_t count;
    mutable std::mutex mtx;
    std::condition_variable not_empty;
    std::condition_variable not_full;
};


#endif //CIRCULARBLOCKINGQUEUE_H
