#ifndef SCREENCASTSOURCE_UTILS_HPP
#define SCREENCASTSOURCE_UTILS_HPP

#include <string>
#include <GLES3/gl3.h>
#include <fstream>


inline void writeData(std::string &name, char *data, int size) {
    std::ofstream stream;
    stream.open(name.c_str(), std::ios::out | std::ios::binary);
    stream.write(data, size);
    stream.close();
}

inline void writePixels(std::string &name, int w, int h) {
    int size = w * h * 4;
    char *colorBuffer = new char[size];
    glReadPixels(0, 0, w, h, GL_RGBA, GL_UNSIGNED_BYTE, colorBuffer);

    writeData(name, colorBuffer, size);

    delete[] colorBuffer;
}

inline std::string getVideoMimeTypeName(std::string &mimeType) {
    if (mimeType.find("hevc")!= std::string::npos || mimeType.find("h265")!= std::string::npos) {
        return "hevc";
    } else if (mimeType.find("avc")!= std::string::npos || mimeType.find("h264")!= std::string::npos) {
        return "avc";
    } else {
        return "unknown";
    }
}

inline std::string getAudioMimeTypeName(std::string &mimeType) {
    if (mimeType.find("pcm")!= std::string::npos) {
        return "pcm";
    } else if (mimeType.find("mp4a-latm")!= std::string::npos) {
        return "aac";
    } else if (mimeType.find("opus")!= std::string::npos) {
        return "opus";
    } else if (mimeType.find("flac")!= std::string::npos) {
        return "flac";
    } else {
        return "unknown";
    }
}

inline int roundToNearestFps(int number) {
    std::vector<int> targetNumbers = {0, 45, 60, 72, 90};

    auto nearestIt = std::min_element(targetNumbers.begin(), targetNumbers.end(),
                                      [number](int a, int b) {
                                          return std::abs(a - number) < std::abs(b - number);
                                      });
    return *nearestIt;
}

inline int roundToNearestSampleRate(int number) {
    std::vector<int> targetNumbers = {0, 8000, 11025, 16000, 22050, 32000, 44100, 48000, 88200, 96000, 176400, 192000,
                                      352800, 384000};

    auto nearestIt = std::min_element(targetNumbers.begin(), targetNumbers.end(),
                                      [number](int a, int b) {
                                          return std::abs(a - number) < std::abs(b - number);
                                      });
    return *nearestIt;
}

inline int readInteger(std::string path) {
    std::ifstream stream(path);

    if (!stream) {
        LOGE("open file failed");
        return 0;
    }

    std::string line;
    if (!std::getline(stream, line)) {
        LOGE("read line failed");
        return 0;
    }

    try {
        return std::stoi(line);
    } catch (const std::exception &e) {
        LOGE("invalid argument:%s", e.what());
    }

    return 0;
}

inline int writeInteger(std::string path, int value) {
    std::ofstream stream(path);

    if (!stream) {
        LOGE("open file %s failed, error: %s", path.c_str(), strerror(errno));
        return -1;
    }

    try {
        stream << value;
    } catch (const std::exception &e) {
        LOGE("write integer failed:%s", e.what());
    }

    return 0;
}

#endif //SCREENCASTSOURCE_UTILS_HPP
