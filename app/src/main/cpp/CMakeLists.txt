# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html.
# For more examples on how to use CMake, see https://github.com/android/ndk-samples.

# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.22.1)

# Declares the project name. The project name can be accessed via ${ PROJECT_NAME},
# Since this is the top level CMakeLists.txt, the project name is also accessible
# with ${CMAKE_PROJECT_NAME} (both CMake variables are in-sync within the top level
# build script scope).
project("screencastsource")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON>radle automatically packages shared libraries with your APK.
#
# In this top level CMakeLists.txt, ${CMAKE_PROJECT_NAME} is used to define
# the target library name; in the sub-module's CMakeLists.txt, ${PROJECT_NAME}
# is preferred for the same purpose.
#
# In order to load a library into your app from Java/Kotlin, you must call
# System.loadLibrary() and pass the name of the library defined here;
# for GameActivity/NativeActivity derived applications, the same library name must be
# used in the AndroidManifest.xml file.

include_directories(
        ${PROJECT_SOURCE_DIR}
        ${PROJECT_SOURCE_DIR}/libs/YVRNet/include
        ${PROJECT_SOURCE_DIR}/libs/DpCamera
        ${PROJECT_SOURCE_DIR}/libs/TinyALSA/include
)

add_library(${CMAKE_PROJECT_NAME} SHARED
        # List C/C++ source files with relative paths to this CMakeLists.txt.
        connector/IConnector.cpp
        connector/StreamConnector.cpp
        connector/YVRNet.cpp
        media/AudioCapture.cpp
        media/AudioEncoder.cpp
        media/Capture.cpp
        media/Encoder.cpp
        media/MediaCapture.cpp
        media/VideoCapture.cpp
        media/VideoEncoder.cpp
        media/AudioQueue.cpp
        media/VideoQueue.cpp
        media/AudioDecoder.cpp
        media/AudioPlayer.cpp
        media/Decoder.cpp
        media/VideoPlayerQueue.cpp
        media/VideoPlayer.cpp
        media/VideoDecoder.cpp
        media/Player.cpp
        media/MediaPlayer.cpp
        graphics/EGLContextEnv.cpp
        graphics/GLGeometry.cpp
        graphics/GLShader.cpp
        graphics/GLRenderExt.cpp
        graphics/GraphicsRender.cpp
        ScreenCastSource.cpp
        ScreenCastSink.cpp
        StreamerSink.cpp
        Streamer.cpp
        native-lib.cpp
        utils/Statistics.cpp
)

# Specifies libraries CMake should link to your target library. You
# can link libraries from various origins, such as libraries defined in this
# build script, prebuilt third-party libraries, or Android system libraries.

add_library(yvrnet SHARED IMPORTED)
set_target_properties(yvrnet PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/libs/YVRNet/${ANDROID_ABI}/libyvrnet.so)

add_library(tinyalsa SHARED IMPORTED)
set_target_properties(tinyalsa PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/libs/TinyALSA/${ANDROID_ABI}/libtinyalsa.so)

add_library(dpcamera SHARED IMPORTED)
set_target_properties(dpcamera PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/libs/DpCamera/${ANDROID_ABI}/libdpcamera.so)

message("CMAKE MERGE_SYSTEM_NATIVE_LIBS ${MERGE_SYSTEM_NATIVE_LIBS}")
set(PROJECT_LINK_DIR ${CMAKE_CURRENT_SOURCE_DIR}/libs/DpCamera/${ANDROID_ABI})
link_directories(${PROJECT_LINK_DIR})
if (MERGE_SYSTEM_NATIVE_LIBS STREQUAL "true")
    set(PROJECT_LINK_LIBS
            ${PROJECT_LINK_DIR}/libprocessgroup.so
            ${PROJECT_LINK_DIR}/libvndksupport.so
            ${PROJECT_LINK_DIR}/libc++.so
            ${PROJECT_LINK_DIR}/libbase.so
            ${PROJECT_LINK_DIR}/libcgrouprc.so
            ${PROJECT_LINK_DIR}/libdl_android.so
            ${PROJECT_LINK_DIR}/ld-android.so
            ${PROJECT_LINK_DIR}/libui.so
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/<EMAIL>
            ${PROJECT_LINK_DIR}/libhardware.so
            ${PROJECT_LINK_DIR}/libhidlbase.so
            ${PROJECT_LINK_DIR}/libhidltransport.so
            ${PROJECT_LINK_DIR}/libhwbinder.so
            ${PROJECT_LINK_DIR}/libbinderthreadstate.so
            ${PROJECT_LINK_DIR}/libbinder.so
            ${PROJECT_LINK_DIR}/libcamera_metadata.so
            ${PROJECT_LINK_DIR}/libfmq.so
            ${PROJECT_LINK_DIR}/libutils.so
            ${PROJECT_LINK_DIR}/libcutils.so
    )
endif ()

target_link_libraries(${CMAKE_PROJECT_NAME}
        # List libraries link to the target library
        ${PROJECT_LINK_LIBS}
        nativewindow
        EGL
        GLESv3
        mediandk
        aaudio
        dpcamera
        tinyalsa
        yvrnet
        android
        log
)

