#include <jni.h>
#include <unistd.h>
#include "ScreenCastSource.h"
#include <string>
#include "ScreenCastSink.h"

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeInit(JNIEnv *env, jobject thiz) {
    // TODO: implement init()
    //ScreenCastSource::instance().init(env, thiz);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeClear(JNIEnv *env, jobject thiz) {
    // TODO: implement clear()
    //ScreenCastSource::instance().clear(env);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeConfigure(JNIEnv *env, jobject thiz, jint id,
                                                              jobject configure) {
    // TODO: implement nativeConfigure()
    //ScreenCastSource::instance().configure(env, id, configure);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeStart(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStart()
    //ScreenCastSource::instance().start(env, id, flag);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeStop(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStop()
    //ScreenCastSource::instance().stop(env, id, flag);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeIsRunning(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement nativeIsRunning()
    return false;//ScreenCastSource::instance().isRunning(id);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeIsStreaming(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement isStreaming()
    return false;//ScreenCastSource::instance().isStreaming(id);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeSetProcessAffinity(JNIEnv *env, jobject thiz) {
    // TODO: implement nativeSetProcessAffinity()
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(4, &cpuset);
    CPU_SET(5, &cpuset);
    CPU_SET(6, &cpuset);
    CPU_SET(7, &cpuset);

    pid_t pid = getpid();
    if (sched_setaffinity(pid, sizeof(cpu_set_t), &cpuset) == -1) {
        LOGE("set affinity error");
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_nativeSetStatisticsLevel(JNIEnv *env, jobject thiz, jint level) {
    // TODO: implement printfLevel()
    ScreenCastSource::instance().setStatisticsLevel(level);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_saveauddat(JNIEnv *env, jobject thiz, jint id, bool bsave){
    //ScreenCastSource::instance().saveAudDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_saveaudpcm(JNIEnv *env, jobject thiz, jint id, bool bsave){
    //ScreenCastSource::instance().saveAudPcm(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_saveviddat(JNIEnv *env, jobject thiz, jint id, bool bsave){
    //ScreenCastSource::instance().saveVidDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_CaptureService_savevidyuv(JNIEnv *env, jobject thiz, jint id, int savenum){
    //ScreenCastSource::instance().saveVidYuv(id, savenum);
}

static void init(JNIEnv *env, jclass clazz) {
    // TODO: implement init()
    ScreenCastSink::instance().init(env);
}

static void clear(JNIEnv *env, jclass clazz) {
    // TODO: implement clear()
    ScreenCastSink::instance().clear(env);
}

static void start(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement start()
    ScreenCastSink::instance().start(env, id);
}

static void stop(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement stop()
    ScreenCastSink::instance().stop(env, id);
}

static jboolean isRunning(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement isRunning()
    return ScreenCastSink::instance().isRunning(id);
}

static jboolean isStreaming(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement isStreaming()
    return ScreenCastSink::instance().isStreaming(id);
}

static void setFocused(JNIEnv *env, jclass clazz, jboolean focused) {
    // TODO: implement setFocused()
    ScreenCastSink::instance().setFocused(focused);
}

static void setSurface(JNIEnv *env, jclass clazz, jint id, jobject surface) {
    // TODO: implement setSurface()
    ScreenCastSink::instance().setSurface(env, id, surface);
}

static void setSurfaceTexture(JNIEnv *env, jclass clazz, jint id, jobject surface_texture) {
    // TODO: implement setSurfaceTexture()
    ScreenCastSink::instance().setSurfaceTexture(env, id, surface_texture);
}

static void setTexture(JNIEnv *env, jclass clazz, jint id, jint texture) {
    // TODO: implement setTexture()
    ScreenCastSink::instance().setTexture(id, texture);
}

static void setVideoFrameRate(JNIEnv *env, jclass clazz, jint id, jint frame_rate) {
    // TODO: implement setVideoFrameRate()
    ScreenCastSink::instance().setVideoFrameRate(id, frame_rate);
}

static void setVideoFrameDropThreshold(JNIEnv *env, jclass clazz, jint id, jint threshold) {
    // TODO: implement setVideoFrameDropThreshold()
    ScreenCastSink::instance().setVideoFrameDropThreshold(id, threshold);
}

static void JNICALL setAudioFrameDropThreshold(JNIEnv *env, jclass clazz, jint id, jint threshold) {
    // TODO: implement setAudioFrameDropThreshold()
    ScreenCastSink::instance().setAudioFrameDropThreshold(id, threshold);
}

static void setAudioSampleRate(JNIEnv *env, jclass clazz, jint id, jint sample_rate) {
    // TODO: implement setAudioSampleRate()
    ScreenCastSink::instance().setAudioSampleRate(id, sample_rate);
}

static void setIp(JNIEnv *env, jclass clazz, jint id, jstring ip) {
    // TODO: implement setIp()
    const char *cStr = env->GetStringUTFChars(ip, nullptr);
    std::string result(cStr);
    env->ReleaseStringUTFChars(ip, cStr);

    ScreenCastSink::instance().setVideoIp(id, result);
    ScreenCastSink::instance().setAudioIp(id, result);
}

static void setStatisticsLevel(JNIEnv *env, jclass clazz, jint level) {
    // TODO: implement setStatisticLevel()
    ScreenCastSink::instance().setStatisticsLevel(level);
}

static void setSourceBuildInfo(JNIEnv *env, jclass clazz, jstring version, jlong time) {
    // TODO: implement setBuildInfo()
    const char *cStr = env->GetStringUTFChars(version, nullptr);
    std::string result(cStr);
    env->ReleaseStringUTFChars(version, cStr);

    ScreenCastSink::instance().setSourceBuildInfo(result, time);
}

static void setTraceLevel(JNIEnv *env, jclass clazz, jint level) {
    ScreenCastSink::instance().setTraceLevel(level);
}

static void onFrameAvailable(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement onFrameAvailable()
    ScreenCastSink::instance().onFrameAvailable(id);
}

static void clearAvailable(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement clearAvailable()
    ScreenCastSink::instance().clearAvailable(id);
}

const char *class_name = "com/pfdm/screencastsource/PlayerNative";

static const JNINativeMethod methods[] = {
        {"init",                       "()V",                                   (void *) init},
        {"clear",                      "()V",                                   (void *) clear},
        {"start",                      "(I)V",                                  (void *) start},
        {"stop",                       "(I)V",                                  (void *) stop},
        {"isRunning",                  "(I)Z",                                  (void *) isRunning},
        {"isStreaming",                "(I)Z",                                  (void *) isStreaming},
        {"setFocused",                 "(Z)V",                                  (void *) setFocused},
        {"setSurface",                 "(ILandroid/view/Surface;)V",            (void *) setSurface},
        {"setSurfaceTexture",          "(ILandroid/graphics/SurfaceTexture;)V", (void *) setSurfaceTexture},
        {"setTexture",                 "(II)V",                                 (void *) setTexture},
        {"setVideoFrameRate",          "(II)V",                                 (void *) setVideoFrameRate},
        {"setVideoFrameDropThreshold", "(II)V",                                 (void *) setVideoFrameDropThreshold},
        {"setAudioFrameDropThreshold", "(II)V",                                 (void *) setAudioFrameDropThreshold},
        {"setAudioSampleRate",         "(II)V",                                 (void *) setAudioSampleRate},
        {"setIp",                      "(ILjava/lang/String;)V",                (void *) setIp},
        {"setStatisticsLevel",         "(I)V",                                  (void *) setStatisticsLevel},
        {"setSourceBuildInfo",         "(Ljava/lang/String;J)V",                (void *) setSourceBuildInfo},
        {"setTraceLevel",              "(I)V",                                  (void *) setTraceLevel},
        {"onFrameAvailable",           "(I)V",                                  (void *) onFrameAvailable},
        {"clearAvailable",             "(I)V",                                  (void *) clearAvailable},
};

jint JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    if (vm->GetEnv(reinterpret_cast<void **>(&env), JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }

    jclass clazz = env->FindClass(class_name);
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0]))) {
        return JNI_ERR;
    }

    return JNI_VERSION_1_6;
}
