#include <jni.h>
#include <unistd.h>
#include "ScreenCastSource.h"

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeInit(JNIEnv *env, jobject thiz) {
    // TODO: implement init()
    ScreenCastSource::instance().init(env, thiz);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeClear(JNIEnv *env, jobject thiz) {
    // TODO: implement clear()
    ScreenCastSource::instance().clear(env);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeConfigure(JNIEnv *env, jobject thiz, jint id,
                                                                 jobject configure) {
    // TODO: implement nativeConfigure()
    ScreenCastSource::instance().configure(env, id, configure);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeStart(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStart()
    ScreenCastSource::instance().start(env, id, flag);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeStop(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStop()
    ScreenCastSource::instance().stop(env, id, flag);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeIsRunning(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement nativeIsRunning()
    return ScreenCastSource::instance().isRunning(id);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeIsStreaming(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement isStreaming()
    return ScreenCastSource::instance().isStreaming(id);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeSetProcessAffinity(JNIEnv *env, jobject thiz) {
    // TODO: implement nativeSetProcessAffinity()
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(4, &cpuset);
    CPU_SET(5, &cpuset);
    CPU_SET(6, &cpuset);
    CPU_SET(7, &cpuset);

    pid_t pid = getpid();
    if (sched_setaffinity(pid, sizeof(cpu_set_t), &cpuset) == -1) {
        LOGE("set affinity error");
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeSetStatisticsLevel(JNIEnv *env, jobject thiz, jint level) {
    // TODO: implement printfLevel()
    ScreenCastSource::instance().setStatisticsLevel(level);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveauddat(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveAudDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveaudpcm(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveAudPcm(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveviddat(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveVidDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_savevidyuv(JNIEnv *env, jobject thiz,jint id, int savenum){
    ScreenCastSource::instance().saveVidYuv(id, savenum);
}
