#include <jni.h>
#include <unistd.h>
#include "ScreenCastSource.h"
#include <string>
#include "ScreencastSink.h"

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeInit(JNIEnv *env, jobject thiz) {
    // TODO: implement init()
    ScreenCastSource::instance().init(env, thiz);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeClear(JNIEnv *env, jobject thiz) {
    // TODO: implement clear()
    ScreenCastSource::instance().clear(env);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeConfigure(JNIEnv *env, jobject thiz, jint id,
                                                                 jobject configure) {
    // TODO: implement nativeConfigure()
    ScreenCastSource::instance().configure(env, id, configure);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeStart(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStart()
    ScreenCastSource::instance().start(env, id, flag);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeStop(JNIEnv *env, jobject thiz, jint id, jint flag) {
    // TODO: implement nativeStop()
    ScreenCastSource::instance().stop(env, id, flag);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeIsRunning(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement nativeIsRunning()
    return ScreenCastSource::instance().isRunning(id);
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeIsStreaming(JNIEnv *env, jobject thiz, jint id) {
    // TODO: implement isStreaming()
    return ScreenCastSource::instance().isStreaming(id);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeSetProcessAffinity(JNIEnv *env, jobject thiz) {
    // TODO: implement nativeSetProcessAffinity()
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(4, &cpuset);
    CPU_SET(5, &cpuset);
    CPU_SET(6, &cpuset);
    CPU_SET(7, &cpuset);

    pid_t pid = getpid();
    if (sched_setaffinity(pid, sizeof(cpu_set_t), &cpuset) == -1) {
        LOGE("set affinity error");
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_nativeSetStatisticsLevel(JNIEnv *env, jobject thiz, jint level) {
    // TODO: implement printfLevel()
    ScreenCastSource::instance().setStatisticsLevel(level);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveauddat(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveAudDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveaudpcm(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveAudPcm(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_saveviddat(JNIEnv *env, jobject thiz,jint id, bool bsave){
    ScreenCastSource::instance().saveVidDat(id, bsave);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_pfdm_screencastsource_ScreenCastService_savevidyuv(JNIEnv *env, jobject thiz,jint id, int savenum){
    ScreenCastSource::instance().saveVidYuv(id, savenum);
}

static void init(JNIEnv *env, jclass clazz) {
    // TODO: implement init()
    ScreencastSink::instance().init(env);
}

static void clear(JNIEnv *env, jclass clazz) {
    // TODO: implement clear()
    ScreencastSink::instance().clear(env);
}

static void start(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement start()
    ScreencastSink::instance().start(env, id);
}

static void stop(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement stop()
    ScreencastSink::instance().stop(env, id);
}

static jboolean isRunning(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement isRunning()
    return ScreencastSink::instance().isRunning(id);
}

static jboolean isStreaming(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement isStreaming()
    return ScreencastSink::instance().isStreaming(id);
}

static void setFocused(JNIEnv *env, jclass clazz, jboolean focused) {
    // TODO: implement setFocused()
    ScreencastSink::instance().setFocused(focused);
}

static void setSurface(JNIEnv *env, jclass clazz, jint id, jobject surface) {
    // TODO: implement setSurface()
    ScreencastSink::instance().setSurface(env, id, surface);
}

static void setSurfaceTexture(JNIEnv *env, jclass clazz, jint id, jobject surface_texture) {
    // TODO: implement setSurfaceTexture()
    ScreencastSink::instance().setSurfaceTexture(env, id, surface_texture);
}

static void setTexture(JNIEnv *env, jclass clazz, jint id, jint texture) {
    // TODO: implement setTexture()
    ScreencastSink::instance().setTexture(id, texture);
}

static void setVideoFrameRate(JNIEnv *env, jclass clazz, jint id, jint frame_rate) {
    // TODO: implement setVideoFrameRate()
    ScreencastSink::instance().setVideoFrameRate(id, frame_rate);
}

static void setVideoFrameDropThreshold(JNIEnv *env, jclass clazz, jint id, jint threshold) {
    // TODO: implement setVideoFrameDropThreshold()
    ScreencastSink::instance().setVideoFrameDropThreshold(id, threshold);
}

static void JNICALL setAudioFrameDropThreshold(JNIEnv *env, jclass clazz, jint id, jint threshold) {
    // TODO: implement setAudioFrameDropThreshold()
    ScreencastSink::instance().setAudioFrameDropThreshold(id, threshold);
}

static void setAudioSampleRate(JNIEnv *env, jclass clazz, jint id, jint sample_rate) {
    // TODO: implement setAudioSampleRate()
    ScreencastSink::instance().setAudioSampleRate(id, sample_rate);
}

static void setIp(JNIEnv *env, jclass clazz, jint id, jstring ip) {
    // TODO: implement setIp()
    const char *cStr = env->GetStringUTFChars(ip, nullptr);
    std::string result(cStr);
    env->ReleaseStringUTFChars(ip, cStr);

    ScreencastSink::instance().setVideoIp(id, result);
    ScreencastSink::instance().setAudioIp(id, result);
}

static void setStatisticsLevel(JNIEnv *env, jclass clazz, jint level) {
    // TODO: implement setStatisticLevel()
    ScreencastSink::instance().setStatisticsLevel(level);
}

static void setSourceBuildInfo(JNIEnv *env, jclass clazz, jstring version, jlong time) {
    // TODO: implement setBuildInfo()
    const char *cStr = env->GetStringUTFChars(version, nullptr);
    std::string result(cStr);
    env->ReleaseStringUTFChars(version, cStr);

    ScreencastSink::instance().setSourceBuildInfo(result, time);
}

static void setTraceLevel(JNIEnv *env, jclass clazz, jint level) {
    ScreencastSink::instance().setTraceLevel(level);
}

static void onFrameAvailable(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement onFrameAvailable()
    ScreencastSink::instance().onFrameAvailable(id);
}

static void clearAvailable(JNIEnv *env, jclass clazz, jint id) {
    // TODO: implement clearAvailable()
    ScreencastSink::instance().clearAvailable(id);
}

const char *class_name = "com/pfdm/screencastsink/ScreencastSinkNative";

static const JNINativeMethod methods[] = {
        {"init",                       "()V",                                   (void *) init},
        {"clear",                      "()V",                                   (void *) clear},
        {"start",                      "(I)V",                                  (void *) start},
        {"stop",                       "(I)V",                                  (void *) stop},
        {"isRunning",                  "(I)Z",                                  (void *) isRunning},
        {"isStreaming",                "(I)Z",                                  (void *) isStreaming},
        {"setFocused",                 "(Z)V",                                  (void *) setFocused},
        {"setSurface",                 "(ILandroid/view/Surface;)V",            (void *) setSurface},
        {"setSurfaceTexture",          "(ILandroid/graphics/SurfaceTexture;)V", (void *) setSurfaceTexture},
        {"setTexture",                 "(II)V",                                 (void *) setTexture},
        {"setVideoFrameRate",          "(II)V",                                 (void *) setVideoFrameRate},
        {"setVideoFrameDropThreshold", "(II)V",                                 (void *) setVideoFrameDropThreshold},
        {"setAudioFrameDropThreshold", "(II)V",                                 (void *) setAudioFrameDropThreshold},
        {"setAudioSampleRate",         "(II)V",                                 (void *) setAudioSampleRate},
        {"setIp",                      "(ILjava/lang/String;)V",                (void *) setIp},
        {"setStatisticsLevel",         "(I)V",                                  (void *) setStatisticsLevel},
        {"setSourceBuildInfo",         "(Ljava/lang/String;J)V",                (void *) setSourceBuildInfo},
        {"setTraceLevel",              "(I)V",                                  (void *) setTraceLevel},
        {"onFrameAvailable",           "(I)V",                                  (void *) onFrameAvailable},
        {"clearAvailable",             "(I)V",                                  (void *) clearAvailable},
};

jint JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    if (vm->GetEnv(reinterpret_cast<void **>(&env), JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }

    jclass clazz = env->FindClass(class_name);
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0]))) {
        return JNI_ERR;
    }

    return JNI_VERSION_1_6;
}
