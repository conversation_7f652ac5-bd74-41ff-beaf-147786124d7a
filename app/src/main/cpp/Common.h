#ifndef SCREENCASTSOURCE_COMMON_H
#define SCREENCASTSOURCE_COMMON_H

#include <android/log.h>
#include <jni.h>
#include <chrono>

#define CAMERA_RESOLVE_NUM 5

#define LOG(...) __android_log_print(ANDROID_LOG_VERBOSE, "ScreenCastSourceNative", __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, "ScreenCastSourceNative", __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, "ScreenCastSourceNative", __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, "ScreenCastSourceNative", __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, "ScreenCastSourceNative", __VA_ARGS__)

typedef struct {
    JNIEnv *env;
    JavaVM *jvm;
    jobject instance;
} AppContext;

typedef enum {
    DEVICE_ID_DP = 0,
    DEVICE_ID_HDMI,
    DEVICE_ID_MAX
} DeviceID;

typedef enum {
    AUDIO_CODEC_UNKNOWN = 0,
    AUDIO_CODEC_AAC,
    AUDIO_CODEC_PCM
} AudioCodec;

typedef enum {
    VIDEO_CODEC_UNKNOWN = 0,
    VIDEO_CODEC_H264,
    VIDEO_CODEC_H265
} VideoCodec;

typedef struct ManagerStatus{
    int width;
    int height;
    int fps;
}ManagerStatus;

typedef enum
{
    MEDIACODEC_AUDIO_UNKNOWN = 0,  // unknow
    MEDIACODEC_AUDIO_AAC = 1,      // AAC
    MEDIACODEC_AUDIO_OPUS = 2,
    MEDIACODEC_AUDIO_PCM = 3,
    MEDIACODEC_AUDIO_PCMA = 4,
    MEDIACODEC_AUDIO_PCMU = 5,
    MEDIACODEC_AUDIO_FLAC = 6,
} DeviceAudioCodec;

typedef enum
{
    MEDIACODEC_VIDEO_UNKNOWN = 0,  // unknow
    MEDIACODEC_VIDEO_H264 = 1,      // AAC
    MEDIACODEC_VIDEO_H265 = 2,
} DeviceVideoCodec;

typedef enum sfErrorCode
{
    SF_EC_OK = 0,
    SF_EC_NODEVICES = 1,
    SF_EC_NOOBJECT = 2,
    SF_EC_JNI_ERROR = 3,
    SF_EC_JNI_EXCEPTION = 4,
    SF_EC_NOUDP = 5,
    SF_EC_EXIST = 6,
    SF_EC_OUT_MAXDEVICES = 7,
    SF_EC_NOMEMORY = 8,

} sfErrorCode;

typedef enum ProtocolType{
    YvrNetType = 0,
    SocketType,  // 1
}ProtocolType;


#pragma pack(push, 1)
typedef struct YvrnetStatus {
    int capID;
    int sendID;
    int streamID;
    uint64_t u64FrameBeginTime;
    uint64_t u64CapTime;
    uint64_t u64EncTime;
    uint64_t u64SendTime;
    uint64_t u64DecTime;
    uint64_t u64InterTime;
    int dwFrameLen;
    bool bVideoFrame;
    int reserved1;
    int reserved2;
} YvrnetStatus;

typedef struct YvrencStatus {
    int ID;
    uint64_t u64FrameBeginTime;
    uint64_t u64NowTime;
    uint64_t u64EncTime;
    uint64_t u64RenderTime;
    uint64_t u64SendTime;
    uint64_t u64DecTime;
    int dwEncSize;
} YvrencStatus;

typedef struct YvrnetAudioStatus {
    int ID;
    int streamID;
    int channel;
    int samplerate;
    int profile;
    int format;
    uint64_t u64CapTime;
    uint64_t u64EncTime;
    uint64_t u64SendTime;
} YvrnetAudioStatus;

typedef struct YvrStatistic {
    int streamID;
    int dwSendFps;
    float fbitrate;
    float fRenderAvg;
    float fnetAvg;
    float fencAvg;
    int dwencFps;
    float fdecAvg;
    float ftotalAvg;
} YvrStatistic;
#pragma pack(pop)

class JNIThreadAttacher {
public:
    JNIThreadAttacher(JavaVM *jvm) {
        // vm_ = AndroidRuntime::getJavaVM();
        jvm_ = jvm;
        status_ = jvm_->GetEnv((void **) &env_, JNI_VERSION_1_6);

        if (status_ != JNI_OK && status_ != JNI_EDETACHED) {
            LOGE("JNIThreadAttacher: unable to get environment for JNI CALL, status: %d", status_);
            env_ = nullptr;
            return;
        }

        if (status_ == JNI_EDETACHED) {
            if (jvm_->AttachCurrentThread(&env_, 0) != 0) {
                LOGE("JNIThreadAttacher: unable to attach thread to VM");
                env_ = nullptr;
                return;
            }
        }
    }

    ~JNIThreadAttacher() {
        if (status_ == JNI_EDETACHED)
            jvm_->DetachCurrentThread();
    }

    JNIEnv *getEnv() { return env_; }

private:
    JavaVM *jvm_;
    JNIEnv *env_;
    jint status_;
};

inline long getCurrentTimestampUs() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch());
    return static_cast<long>(duration.count());
}

#endif //SCREENCASTSOURCE_COMMON_H
