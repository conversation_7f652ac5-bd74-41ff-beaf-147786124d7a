#include "ScreenCastSink.h"
#include "utils/Statistics.hpp"
#include <android/surface_texture_jni.h>

#define MIME_TYPE_VIDEO_HEVC "video/hevc"
#define MIME_TYPE_AUDIO_MP4A_LATM "audio/mp4a-latm"

ScreenCastSink::ScreenCastSink() {
    mConfig[DEVICE_ID_DP].id = DEVICE_ID_DP;
    mConfig[DEVICE_ID_DP].jSurfaceTexture = nullptr;
    mConfig[DEVICE_ID_DP].surfaceTexture = nullptr;
    mConfig[DEVICE_ID_DP].window = nullptr;
    mConfig[DEVICE_ID_DP].player.id = DEVICE_ID_DP;
    mConfig[DEVICE_ID_DP].player.trace = 0;
    mConfig[DEVICE_ID_DP].player.versionName = "";
    mConfig[DEVICE_ID_DP].player.buildTime = 0;
    mConfig[DEVICE_ID_DP].player.video.port = "1888";
    mConfig[DEVICE_ID_DP].player.video.width = 1920;
    mConfig[DEVICE_ID_DP].player.video.height = 1080;
    mConfig[DEVICE_ID_DP].player.video.frameRate = 60;
    mConfig[DEVICE_ID_DP].player.video.dropThreshold = 2;
    mConfig[DEVICE_ID_DP].player.video.mimeType = MIME_TYPE_VIDEO_HEVC;

    mConfig[DEVICE_ID_DP].player.audio.port = "1890";
    mConfig[DEVICE_ID_DP].player.audio.channelCount = 2;
    mConfig[DEVICE_ID_DP].player.audio.sampleRate = 48000;
    mConfig[DEVICE_ID_DP].player.audio.dropThreshold = 10;
    mConfig[DEVICE_ID_DP].player.audio.mimeType = MIME_TYPE_AUDIO_MP4A_LATM;

    mConfig[DEVICE_ID_HDMI].id = DEVICE_ID_HDMI;
    mConfig[DEVICE_ID_HDMI].jSurfaceTexture = nullptr;
    mConfig[DEVICE_ID_HDMI].surfaceTexture = nullptr;
    mConfig[DEVICE_ID_HDMI].window = nullptr;
    mConfig[DEVICE_ID_HDMI].player.id = DEVICE_ID_HDMI;
    mConfig[DEVICE_ID_HDMI].player.trace = 0;
    mConfig[DEVICE_ID_HDMI].player.versionName = "";
    mConfig[DEVICE_ID_HDMI].player.buildTime = 0;
    mConfig[DEVICE_ID_HDMI].player.video.port = "1896";
    mConfig[DEVICE_ID_HDMI].player.video.width = 1920;
    mConfig[DEVICE_ID_HDMI].player.video.height = 1080;
    mConfig[DEVICE_ID_HDMI].player.video.frameRate = 60;
    mConfig[DEVICE_ID_HDMI].player.video.dropThreshold = 2;
    mConfig[DEVICE_ID_HDMI].player.video.mimeType = MIME_TYPE_VIDEO_HEVC;

    mConfig[DEVICE_ID_HDMI].player.audio.port = "1898";
    mConfig[DEVICE_ID_HDMI].player.audio.channelCount = 2;
    mConfig[DEVICE_ID_HDMI].player.audio.sampleRate = 48000;
    mConfig[DEVICE_ID_HDMI].player.audio.dropThreshold = 10;
    mConfig[DEVICE_ID_HDMI].player.audio.mimeType = MIME_TYPE_AUDIO_MP4A_LATM;

    mDPCallback = new StreamerSinkCallback(DEVICE_ID_DP, this);
    mHDMICallback = new StreamerSinkCallback(DEVICE_ID_HDMI, this);

    mStreamerSink[DEVICE_ID_DP].setCallback(mDPCallback);
    mStreamerSink[DEVICE_ID_HDMI].setCallback(mHDMICallback);
}

ScreenCastSink::~ScreenCastSink() {
    mStreamerSink[DEVICE_ID_DP].setCallback(nullptr);
    mStreamerSink[DEVICE_ID_HDMI].setCallback(nullptr);

    delete mDPCallback;
    delete mHDMICallback;
}

void ScreenCastSink::init(JNIEnv *env) {
    mContext.env = env;
    env->GetJavaVM(&mContext.jvm);
    jclass clazz = env->FindClass("com/pfdm/screencastsource/PlayerNative");
    mContext.clazz = (jclass) (env->NewGlobalRef(clazz));

    mOnStateChangedMethod = env->GetStaticMethodID(mContext.clazz, "onStateChanged", "(II)V");
    mOnVideoSizeChangedMethod = env->GetStaticMethodID(mContext.clazz, "onVideoSizeChanged", "(III)V");
}

void ScreenCastSink::clear(JNIEnv *env) {
    env->DeleteGlobalRef(mContext.clazz);
    if (mConfig[DEVICE_ID_DP].jSurfaceTexture != nullptr) {
        env->DeleteGlobalRef(mConfig[DEVICE_ID_DP].jSurfaceTexture);
    }
    if (mConfig[DEVICE_ID_HDMI].jSurfaceTexture != nullptr) {
        env->DeleteGlobalRef(mConfig[DEVICE_ID_HDMI].jSurfaceTexture);
    }
}

void ScreenCastSink::start(JNIEnv *env, int id) {
    LOGD("start env:%p id:%d", env, id);
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);

    if (mStreamerSink[value].isRunning()) {
        LOGW("[%d]StreamerSink has been started", value);
        return;
    }

    if(mConfig[value].jSurfaceTexture != nullptr) {
        ASurfaceTexture *aSurfaceTexture = ASurfaceTexture_fromSurfaceTexture(env, mConfig[value].jSurfaceTexture);
        mConfig[value].surfaceTexture = aSurfaceTexture;
        mConfig[value].window = ASurfaceTexture_acquireANativeWindow(aSurfaceTexture);
    }

    mConfig[value].id = value;
    mStreamerSink[value].configure(mConfig[value]);
    mStreamerSink[value].start();
}

void ScreenCastSink::stop(JNIEnv *env, int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);

    if (!mStreamerSink[value].isRunning()) {
        LOGW("[%d]StreamerSink has been stopped", value);
        return;
    }

    mStreamerSink[value].stop();

    env->CallStaticVoidMethod(mContext.clazz, mOnStateChangedMethod, value, 0);

    ANativeWindow_release(mConfig[value].window);
    if(mConfig[value].surfaceTexture != nullptr) {
        ASurfaceTexture_release(mConfig[value].surfaceTexture);
    }
}

bool ScreenCastSink::isRunning(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return mStreamerSink[value].isRunning();
}

bool ScreenCastSink::isStreaming(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return mStreamerSink[value].isConnected();
}

void ScreenCastSink::setVideoFrameDropThreshold(int id, int threshold) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    int th = std::clamp(threshold, 1, 20);
    mConfig[value].player.video.dropThreshold = th;
    mStreamerSink[value].setVideoFrameDropThreshold(th);
}

void ScreenCastSink::setAudioFrameDropThreshold(int id, int threshold) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    int th = std::clamp(threshold, 1, 20);
    mConfig[value].player.audio.dropThreshold = th;
    mStreamerSink[value].setAudioFrameDropThreshold(th);
}

void ScreenCastSink::setFocused(bool focused) {
    mStreamerSink[DEVICE_ID_DP].setFocused(focused);
    mStreamerSink[DEVICE_ID_HDMI].setFocused(focused);
}

void ScreenCastSink::setSurface(JNIEnv *env, int id, jobject surface) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].window = ANativeWindow_fromSurface(env, surface);
}

void ScreenCastSink::setSurfaceTexture(JNIEnv *env, int id, jobject surfaceTexture) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].jSurfaceTexture = env->NewGlobalRef(surfaceTexture);
//    ASurfaceTexture *aSurfaceTexture = ASurfaceTexture_fromSurfaceTexture(env, surfaceTexture);
//    mConfig[value].surfaceTexture = aSurfaceTexture;
//    mConfig[value].window = ASurfaceTexture_acquireANativeWindow(aSurfaceTexture);
}

void ScreenCastSink::setTexture(int id, int texture) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
}

void ScreenCastSink::setVideoFrameRate(int id, int frameRate) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].player.video.frameRate = frameRate;
}

void ScreenCastSink::setAudioSampleRate(int id, int sampleRate) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    if (sampleRate == 0) {
        LOGE("invalid sample rate, set to 48000");
        sampleRate = 48000;
    }

    mConfig[value].player.audio.sampleRate = sampleRate;
}

void ScreenCastSink::setVideoIp(int id, std::string ip) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].player.video.ip = ip;
}

void ScreenCastSink::setVideoPort(int id, std::string port) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].player.video.port = port;
}

void ScreenCastSink::setAudioIp(int id, std::string ip) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].player.audio.ip = ip;
}

void ScreenCastSink::setAudioPort(int id, std::string port) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    mConfig[value].player.audio.port = port;
}

void ScreenCastSink::setStatisticsLevel(int level) {
    Statistics::Instance().PrintfLevel(level);
}

void ScreenCastSink::setSourceBuildInfo(std::string version, long time) {
    for (StreamerSink::Config &config: mConfig) {
        config.player.versionName = version;
        config.player.buildTime = time;
    }
}

void ScreenCastSink::setTraceLevel(int level) {
    for (StreamerSink::Config &config: mConfig) {
        config.player.trace = level;
    }
}

void ScreenCastSink::onFrameAvailable(int id) {
    mStreamerSink[id].onFrameAvailable();
}

void ScreenCastSink::clearAvailable(int id) {
    mStreamerSink[id].clearAvailable();
}

void ScreenCastSink::onVideoSizeChanged(DeviceID id, int width, int height) const {
    LOGI("[%d]on StreamerSink video size changed, w:%d h:%d", id, width, height);
    JNIThreadAttacher attacher(mContext.jvm);
    attacher.getEnv()->CallStaticVoidMethod(mContext.clazz, mOnVideoSizeChangedMethod, (int) id, width, height);
}

void ScreenCastSink::onConnectionChanged(DeviceID id, bool connected) const {
    LOGI("[%d]on StreamerSink connection changed, connected:%d", id, connected);
    JNIThreadAttacher attacher(mContext.jvm);
    attacher.getEnv()->CallStaticVoidMethod(mContext.clazz, mOnStateChangedMethod, (int) id, connected ? 1 : 0);
}

void ScreenCastSink::StreamerSinkCallback::onVideoSizeChanged(int width, int height) {
    mSink->onVideoSizeChanged(mID, width, height);
}

void ScreenCastSink::StreamerSinkCallback::onConnectionChanged(bool connected) {
    mSink->onConnectionChanged(mID, connected);
}
