#include "EGLContextEnv.h"
#include "Common.h"
#include "GLRenderExt.h"

EGLContextEnv::EGLContextEnv() = default;

EGLContextEnv::~EGLContextEnv() = default;

bool EGLContextEnv::initialize(ANativeWindow *nativeWindow) {
    mDisplay = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    LOGI("init display %p", mDisplay);
    if (eglInitialize(mDisplay, &mMajorVersion, &mMinorVersion) == EGL_FALSE) {
        LOGE("eglInitialize() failed: %s", errorToString(eglGetError()));
        return false;
    }

    // Do NOT use eglChooseConfig, because the Android EGL code pushes in multisample
    // flags in eglChooseConfig if the user has selected the "force 4x MSAA" option in
    // settings, and that is completely wasted for the time warp renderer.
    const int MAX_CONFIGS = 1024;
    EGLConfig configs[MAX_CONFIGS];
    EGLint numConfigs = 0;
    if (eglGetConfigs(mDisplay, configs, MAX_CONFIGS, &numConfigs) == EGL_FALSE) {
        LOGE("eglGetConfigs() failed: %s", errorToString(eglGetError()));
        return false;
    }

    const EGLint configAttribs[] = {EGL_RED_SIZE, 8,
                                    EGL_GREEN_SIZE, 8,
                                    EGL_BLUE_SIZE, 8,
                                    EGL_ALPHA_SIZE, 8,
                                    EGL_DEPTH_SIZE, 0,
                                    EGL_STENCIL_SIZE, 0,
                                    EGL_SAMPLES, 0,
                                    EGL_NONE};

    for (int i = 0; i < numConfigs; ++i) {
        EGLint value = 0;
        eglGetConfigAttrib(mDisplay, configs[i], EGL_RENDERABLE_TYPE, &value);

        // Check EGL Client Version
        if ((value & EGL_OPENGL_ES3_BIT_KHR) != EGL_OPENGL_ES3_BIT_KHR) {
            continue;
        }

        // EGL_WINDOW_BIT is required so it can share textures with the window context.
        eglGetConfigAttrib(mDisplay, configs[i], EGL_SURFACE_TYPE, &value);
        if ((value & (EGL_WINDOW_BIT | EGL_PBUFFER_BIT)) != (EGL_WINDOW_BIT | EGL_PBUFFER_BIT)) {
            continue;
        }

        int j = 0;
        for (; configAttribs[j] != EGL_NONE; j += 2) {
            eglGetConfigAttrib(mDisplay, configs[i], configAttribs[j], &value);
            if (value != configAttribs[j + 1]) {
                break;
            }
        }
        if (configAttribs[j] == EGL_NONE) {
            mConfig = configs[i];
            break;
        }
    }

    if (mConfig == 0) {
        LOGE("eglChooseConfig() failed: %s", errorToString(eglGetError()));
        return false;
    }

    EGLint contextAttribs[] = {EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE};
    mContext = eglCreateContext(mDisplay, mConfig, EGL_NO_CONTEXT, contextAttribs);
    if (mContext == EGL_NO_CONTEXT) {
        LOGE("eglCreateContext() failed: %s", errorToString(eglGetError()));
        return false;
    }

    mSurface = createSurface(nativeWindow);
    if (mSurface == EGL_NO_SURFACE) {
        LOGE("eglCreateWindowSurface() failed: %s", errorToString(eglGetError()));
        eglDestroyContext(mDisplay, mContext);
        mContext = EGL_NO_CONTEXT;
        return false;
    }

    if (!makeCurrent(mSurface)) {
        return false;
    }

    return true;
}

EGLSurface EGLContextEnv::createSurface(ANativeWindow *nativeWindow) {
    EGLSurface surface = EGL_NO_SURFACE;

    if (nativeWindow == nullptr) {
        const EGLint surfaceAttribs[] = {EGL_WIDTH, 16, EGL_HEIGHT, 16, EGL_NONE};
        surface = eglCreatePbufferSurface(mDisplay, mConfig, surfaceAttribs);
    } else {
        surface = eglCreateWindowSurface(mDisplay, mConfig, nativeWindow, NULL);
    }

    return surface;
}

EGLSurface EGLContextEnv::getSurface() {
    return mSurface;
}

bool EGLContextEnv::makeCurrent(EGLSurface surface) {
    if (eglMakeCurrent(mDisplay, surface, surface, mContext) == EGL_FALSE) {
        LOGE("eglMakeCurrent() failed: %s", errorToString(eglGetError()));
        return false;
    }
    return true;
}

bool EGLContextEnv::makeCurrent() {
    return makeCurrent(mSurface);
}

bool EGLContextEnv::swapBuffers(EGLSurface surface) {
    if (eglSwapBuffers(mDisplay, surface) == EGL_FALSE) {
        LOGE("SwapBuffers() failed: %s", errorToString(eglGetError()));
        return false;
    }
    return true;
}

bool EGLContextEnv::swapBuffers() {
    return swapBuffers(mSurface);
}

void EGLContextEnv::clear() {
    if (mDisplay != EGL_NO_DISPLAY) {
        if (eglMakeCurrent(mDisplay, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT) == EGL_FALSE) {
            LOGE("eglMakeCurrent() failed: %s", errorToString(eglGetError()));
        }
    }

    if (mSurface != EGL_NO_SURFACE) {
        if (eglDestroySurface(mDisplay, mSurface) == EGL_FALSE) {
            LOGE("eglDestroySurface() failed: %s", errorToString(eglGetError()));
        }
        mSurface = EGL_NO_SURFACE;
    }

    if (mContext != EGL_NO_CONTEXT) {
        if (eglDestroyContext(mDisplay, mContext) == EGL_FALSE) {
            LOGE("eglDestroyContext() failed: %s", errorToString(eglGetError()));
        }
        mContext = EGL_NO_CONTEXT;
    }

    if (mDisplay != EGL_NO_DISPLAY) {
        if (eglTerminate(mDisplay) == EGL_FALSE) {
            LOGE("eglTerminate() failed: %s", errorToString(eglGetError()));
        }
        mDisplay = EGL_NO_DISPLAY;
    }

    eglReleaseThread();
}

bool EGLContextEnv::presentationTimeANDROID(EGLSurface surface, long timestamp) {
    if (glext::eglPresentationTimeANDROID(mDisplay, surface, timestamp) == EGL_FALSE) {
        LOGE("EglPresentationTimeANDROID() failed: %s", errorToString(eglGetError()));
        return false;
    }
    return true;
}

bool EGLContextEnv::presentationTimeANDROID(long timestamp) {
    return presentationTimeANDROID(mSurface, timestamp);
}

const char *EGLContextEnv::errorToString(const EGLint error) {
    switch (error) {
        case EGL_SUCCESS:
            return "EGL_SUCCESS";
        case EGL_NOT_INITIALIZED:
            return "EGL_NOT_INITIALIZED";
        case EGL_BAD_ACCESS:
            return "EGL_BAD_ACCESS";
        case EGL_BAD_ALLOC:
            return "EGL_BAD_ALLOC";
        case EGL_BAD_ATTRIBUTE:
            return "EGL_BAD_ATTRIBUTE";
        case EGL_BAD_CONTEXT:
            return "EGL_BAD_CONTEXT";
        case EGL_BAD_CONFIG:
            return "EGL_BAD_CONFIG";
        case EGL_BAD_CURRENT_SURFACE:
            return "EGL_BAD_CURRENT_SURFACE";
        case EGL_BAD_DISPLAY:
            return "EGL_BAD_DISPLAY";
        case EGL_BAD_SURFACE:
            return "EGL_BAD_SURFACE";
        case EGL_BAD_MATCH:
            return "EGL_BAD_MATCH";
        case EGL_BAD_PARAMETER:
            return "EGL_BAD_PARAMETER";
        case EGL_BAD_NATIVE_PIXMAP:
            return "EGL_BAD_NATIVE_PIXMAP";
        case EGL_BAD_NATIVE_WINDOW:
            return "EGL_BAD_NATIVE_WINDOW";
        case EGL_CONTEXT_LOST:
            return "EGL_CONTEXT_LOST";
        default:
            return "unknown";
    }
}
