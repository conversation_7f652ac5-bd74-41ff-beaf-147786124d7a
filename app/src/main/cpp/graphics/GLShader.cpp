#include "GLShader.h"
#include "Common.h"

#define MAX_UNIFORM_NAME_LENGTH 64

GLShader::GLShader(const char *vertexSrc, const char *fragmentSrc) {
    const char *vShaderCode = vertexSrc;
    const char *fShaderCode = fragmentSrc;
    // 2. compile shaders
    unsigned int vertex, fragment;
    // vertex shader
    vertex = glCreateShader(GL_VERTEX_SHADER);
    glShaderSource(vertex, 1, &vShaderCode, NULL);
    glCompileShader(vertex);
    checkCompileErrors(vertex, "VERTEX");
    // fragment Shader
    fragment = glCreateShader(GL_FRAGMENT_SHADER);
    glShaderSource(fragment, 1, &fShaderCode, NULL);
    glCompileShader(fragment);
    checkCompileErrors(fragment, "FRAGMENT");
    // shader Program
    ID = glCreateProgram();
    glAttachShader(ID, vertex);
    glAttachShader(ID, fragment);
    glLinkProgram(ID);
    checkCompileErrors(ID, "PROGRAM");
    // delete the shaders as they're linked into our program now and no longer necessary
    glDeleteShader(vertex);
    glDeleteShader(fragment);

    generateName2UniformMap();
}

GLShader::~GLShader() {
    glDeleteProgram(ID);
}

void GLShader::use() {
    glUseProgram(ID);
}

void GLShader::disuse() {
    glUseProgram(0);
}

void GLShader::setBool(const std::string &name, bool value) {
    Uniform &uniform = mUniformMap[name];
    glUniform1i(uniform.location, value);
}

void GLShader::setInt(const std::string &name, int value) {
    Uniform &uniform = mUniformMap[name];
    glUniform1i(uniform.location, value);
}

void GLShader::setFloat(const std::string &name, float value) {
    Uniform &uniform = mUniformMap[name];
    glUniform1f(uniform.location, value);
}

void GLShader::setVec2(const std::string &name, float x, float y) {
    Uniform &uniform = mUniformMap[name];
    glUniform2f(uniform.location, x, y);
}

void GLShader::setVec2(const std::string &name, const GLfloat *value) {
    Uniform &uniform = mUniformMap[name];
    glUniform2fv(uniform.location, 1, value);
}

void GLShader::setVec3(const std::string &name, float x, float y, float z) {
    Uniform &uniform = mUniformMap[name];
    glUniform3f(uniform.location, x, y, z);
}

void GLShader::setVec4(const std::string &name, float x, float y, float z, float w) {
    Uniform &uniform = mUniformMap[name];
    glUniform4f(uniform.location, x, y, z, w);
}

void GLShader::setVec4(const std::string &name, const GLfloat *value) {
    Uniform &uniform = mUniformMap[name];
    glUniform4fv(uniform.location, 1, value);
}

void GLShader::setMat4(const std::string &name, const float *mat) {
    Uniform &uniform = mUniformMap[name];
    glUniformMatrix4fv(uniform.location, 1, GL_FALSE, mat);
}

void GLShader::setSampler(const std::string name, unsigned int target, unsigned int texture) {
    Uniform &uniform = mUniformMap[name];
    glUniform1i(uniform.location, uniform.samplerIndex);

    glActiveTexture(GL_TEXTURE0 + uniform.samplerIndex);
    glBindTexture(target, texture);
}

void GLShader::checkCompileErrors(unsigned int shader, std::string type) {
    int success;
    char infoLog[1024];
    if (type != "PROGRAM") {
        glGetShaderiv(shader, GL_COMPILE_STATUS, &success);
        if (!success) {
            glGetShaderInfoLog(shader, 1024, NULL, infoLog);
            LOG("ERROR::SHADER_COMPILATION_ERROR of type: %s, %s", type.c_str(), infoLog);
        }
    } else {
        glGetProgramiv(shader, GL_LINK_STATUS, &success);
        if (!success) {
            glGetProgramInfoLog(shader, 1024, NULL, infoLog);
            LOG("ERROR::PROGRAM_LINKING_ERROR of type: %s, %s", type.c_str(), infoLog);
        }
    }
}

void GLShader::generateName2UniformMap() {
    int activeUniformsCount;
    mSamplerIndex = 0;
    mUniformMap.clear();

    glGetProgramiv(ID, GL_ACTIVE_UNIFORMS, &activeUniformsCount);
    glUseProgram(ID);

    for (int i = 0; i < activeUniformsCount; i++) {
        static char uniformNameBuffer[MAX_UNIFORM_NAME_LENGTH];
        Uniform uniform{};

        glGetActiveUniform(ID, i, MAX_UNIFORM_NAME_LENGTH - 1, nullptr, nullptr, &uniform.type,
                           &uniformNameBuffer[0]);

        uniform.name = std::string(uniformNameBuffer);
        uniform.location = glGetUniformLocation(ID, uniformNameBuffer);

        if (isSampleUniform(uniform)) {
            uniform.samplerIndex = mSamplerIndex++;
            glUniform1i(uniform.location, uniform.samplerIndex);
        } else {
            uniform.samplerIndex = 0;
        }
        mUniformMap[uniform.name] = uniform;

        LOGI("uniform name %s, location %d, index %d", uniform.name.c_str(), uniform.location,
             uniform.samplerIndex);
    }
}

bool GLShader::isSampleUniform(const Uniform &uniform) {
    return uniform.type == GL_SAMPLER_2D || uniform.type == GL_SAMPLER_2D_ARRAY ||
           uniform.type == GL_SAMPLER_EXTERNAL_OES ||
           uniform.type == GL_SAMPLER_3D || uniform.type == GL_SAMPLER_CUBE ||
           uniform.type == GL_SAMPLER_EXTERNAL_2D_Y2Y_EXT;
}

bool GLShader::isUniformExist(std::string name) const {
    return mUniformMap.find(name) != mUniformMap.end();
}
