#include "GLGeometry.h"
#include "GLRenderExt.h"
#include <GLES3/gl32.h>
#include <string>
#include <vector>

GLGeometry::GLGeometry() {
    mVAO = 0;
    mVBO = 0;
    mEBO = 0;
    mVertexCount = 0;
    mIndexCount = 0;
}

GLGeometry::~GLGeometry() = default;

void GLGeometry::create(ProgramAttribute *pAttribs, int nAttribs, unsigned int *pIndices, int nIndices,
                            const void *pVertices, int nVertices) {
    // Create the VBO
    glGenBuffers(1, &mVBO);
    glBindBuffer(GL_ARRAY_BUFFER, mVBO);
    glBufferData(GL_ARRAY_BUFFER, nVertices, pVertices, GL_STATIC_DRAW);
    glBindBuffer(GL_ARRAY_BUFFER, 0);

    // Create the Index Buffer
    glGenBuffers(1, &mEBO);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, mEBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, nIndices, pIndices, GL_STATIC_DRAW);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);

    // Create the VAO
    glGenVertexArrays(1, &mVAO);

    glBindVertexArray(mVAO);
    glBindBuffer(GL_ARRAY_BUFFER, mVBO);

    for (int i = 0; i < nAttribs; i++) {
        glEnableVertexAttribArray(pAttribs[i].index);
        glVertexAttribPointer(pAttribs[i].index, pAttribs[i].size, pAttribs[i].type, pAttribs[i].normalized,
                              pAttribs[i].stride,
                              (void *) (unsigned long long) (pAttribs[i].offset));
    }
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, mEBO);

    glBindVertexArray(0);
    glBindBuffer(GL_ARRAY_BUFFER, 0);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);

    mVertexCount = nVertices;
    mIndexCount = nIndices;
}

void GLGeometry::destroy() {
    glDeleteVertexArrays(1, &mVAO);
    glDeleteBuffers(1, &mVBO);
    glDeleteBuffers(1, &mEBO);

    mVAO = 0;
    mVBO = 0;
    mEBO = 0;
    mVertexCount = 0;
    mIndexCount = 0;
}

void GLGeometry::submit() const {
    glBindVertexArray(mVAO);
    glDrawElements(GL_TRIANGLES, mIndexCount, GL_UNSIGNED_INT, nullptr);
    glBindVertexArray(0);
}

void GLGeometry::submit(unsigned int sampler, unsigned int texture, unsigned int type) const {
    glBindVertexArray(mVAO);
    glActiveTexture(GL_TEXTURE0 + sampler);
    glBindTexture(type, texture);
    glDrawElements(GL_TRIANGLES, mIndexCount, GL_UNSIGNED_INT, nullptr);
    glBindVertexArray(0);
}
