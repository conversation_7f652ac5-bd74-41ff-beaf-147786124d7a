#include "GLRenderExt.h"
#include "Common.h"

namespace glext {
    PFNEGLCREATESYNCKHRPROC eglCreateSyncKHR = nullptr;
    PFNEGLDESTROYSYNCKHRPROC eglDestroySyncKHR = nullptr;
    PFNEGLCLIENTWAITSYNCKHRPROC eglClientWaitSyncKHR = nullptr;
    PFNGLGENMEMORYOBJECTSKHR glGenMemoryObjectsKHR = nullptr;
    PFNGLIMPORTMEMORYFDKHR glImportMemoryFdKHR = nullptr;
    PFNGLTEXSTORAGEMEM2DKHR glTexStorageMem2DKHR = nullptr;
    PFNGLDELETEMEMORYOBJECTSKHR glDeleteMemoryObjectsKHR = nullptr;
    PFNEGLGETSYNCATTRIBKHRPROC eglGetSyncAttribKHR = nullptr;

// For later
    PFNGLCREATEMEMORYOBJECTSEXT glCreateMemoryObjectsEXT = nullptr;
    PFNGLDELETEMEMORYOBJECTSEXT glDeleteMemoryObjectsEXT = nullptr;
    PFNGLIMPORTMEMORYFDEXT glImportMemoryFdEXT = nullptr;
    PFNGLTEXSTORAGEMEM2DEXT glTexStorageMem2DEXT = nullptr;
    PFNGLSTARTTILINGQCOMPROC glStartTilingQCOM = nullptr;
    PFNGLENDTILINGQCOMPROC glEndTilingQCOM = nullptr;
    PFNGLEGLIMAGETARGETTEXTURE2DOESPROC glEGLImageTargetTexture2DOES = nullptr;
    PFNEGLCREATEIMAGEKHRPROC eglCreateImageKHR = nullptr;
    PFNEGLDESTROYIMAGEKHRPROC eglDestroyImageKHR = nullptr;
    PFNEGLIMAGETARGETTEXSTORAGEEXTPROC eglImageTargetTexStorageEXT = nullptr;
    PFNGLTEXTUREVIEWOESPROC glTextureViewOES = nullptr;
    PFNEGLWAITSYNCKHRPROC eglWaitSyncKHR = nullptr;
    PFNGLTEXSTORAGEMEM3DEXTPROC glTexStorageMem3DEXT = nullptr;
    PFNEGLGETNATIVECLIENTBUFFERANDROIDPROC eglGetNativeClientBufferANDROID = nullptr;
    PFNEGLPRESENTATIONTTIMEANDROIDROC eglPresentationTimeANDROID = nullptr;

    void *initExtension(const char *extName, const char *alternateExtName) {
        void *pExt = (void *) eglGetProcAddress(extName);
        if (nullptr == pExt) {
            if (nullptr != alternateExtName) {
                LOGI("Failed to locate extension: %s", extName);
                LOGI("Trying Function: %s", alternateExtName);
                pExt = (void *) eglGetProcAddress(alternateExtName);
                if (nullptr == pExt) {
                    LOGE("Failed to locate extension: %s", alternateExtName);
                } else {
                    LOGI("Function initialized: %s", alternateExtName);
                }
            } else {
                LOGE("Failed to locate extension: %s", extName);
            }
        }

        return pExt;
    }

    bool initializeRenderExtensions() {
//        eglCreateSyncKHR = (PFNEGLCREATESYNCKHRPROC) initExtension("eglCreateSyncKHR");
//        eglDestroySyncKHR = (PFNEGLDESTROYSYNCKHRPROC) initExtension("eglDestroySyncKHR");
//        eglClientWaitSyncKHR = (PFNEGLCLIENTWAITSYNCKHRPROC) initExtension("eglClientWaitSyncKHR");
//        eglGetSyncAttribKHR = (PFNEGLGETSYNCATTRIBKHRPROC) initExtension("eglGetSyncAttribKHR");
//        glCreateMemoryObjectsEXT = (PFNGLCREATEMEMORYOBJECTSEXT) initExtension("glCreateMemoryObjectsEXT");
//        glImportMemoryFdKHR = (PFNGLIMPORTMEMORYFDKHR) initExtension("glImportMemoryFdKHR",
//                                                                     "glImportMemoryFdEXT");
//        glTexStorageMem2DKHR = (PFNGLTEXSTORAGEMEM2DKHR) initExtension("glTexStorageMem2DKHR",
//                                                                       "glTexStorageMem2DEXT");
//        glDeleteMemoryObjectsKHR = (PFNGLDELETEMEMORYOBJECTSKHR) initExtension("glDeleteMemoryObjectsKHR",
//                                                                               "glDeleteMemoryObjectsEXT");
//        glStartTilingQCOM = (PFNGLSTARTTILINGQCOMPROC) initExtension("glStartTilingQCOM");
//        glEndTilingQCOM = (PFNGLENDTILINGQCOMPROC) initExtension("glEndTilingQCOM");
        glEGLImageTargetTexture2DOES = (PFNGLEGLIMAGETARGETTEXTURE2DOESPROC) initExtension(
                "glEGLImageTargetTexture2DOES");
        eglCreateImageKHR = (PFNEGLCREATEIMAGEKHRPROC) initExtension("eglCreateImageKHR");
        eglDestroyImageKHR = (PFNEGLDESTROYIMAGEKHRPROC) initExtension("eglDestroyImageKHR");
//        eglImageTargetTexStorageEXT = (PFNEGLIMAGETARGETTEXSTORAGEEXTPROC) initExtension(
//                "glEGLImageTargetTexStorageEXT");
//        glTextureViewOES = (PFNGLTEXTUREVIEWOESPROC) initExtension("glTextureViewOES");
//        eglWaitSyncKHR = (PFNEGLWAITSYNCKHRPROC) initExtension("eglWaitSyncKHR");
//        glTexStorageMem3DEXT = (PFNGLTEXSTORAGEMEM3DEXTPROC) initExtension("glTexStorageMem3DEXT");
        eglGetNativeClientBufferANDROID = (PFNEGLGETNATIVECLIENTBUFFERANDROIDPROC) initExtension(
                "eglGetNativeClientBufferANDROID");
        eglPresentationTimeANDROID = (PFNEGLPRESENTATIONTTIMEANDROIDROC) initExtension(
                "eglPresentationTimeANDROID");

        return true;
    }
}

