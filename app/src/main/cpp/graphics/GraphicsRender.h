#ifndef SCREENCASTSOURCE_GRAPHICSRENDER_H
#define SCREENCASTSOURCE_GRAPHICSRENDER_H

#include <memory>
#include <android/hardware_buffer.h>
#include "graphics/GLShader.h"
#include "graphics/GLGeometry.h"
#include "Common.h"
#include <map>

class GraphicsRender {
public:
    explicit GraphicsRender(int id);

    ~GraphicsRender();

    void create();

    void destroy();

    bool setTexture(void *buffer) const;

    bool setTexture(AHardwareBuffer *aHardwareBuffer);

    void render();

    void setSize(int width, int height);

private:
    void createTexture();

    void destroyTexture();

    void createShader();

    void destroyShader();

    void createMesh();

    void destroyMesh();

private:
    int mId;
    int mWidth;
    int mHeight;
    GLuint mTexHandle;
    std::shared_ptr<GLShader> mShader;
    std::shared_ptr<GLGeometry> mGeometry;
    std::map<uintptr_t, std::pair<GLuint, EGLImage>> mEGLImageMap;
};


#endif //SCREENCASTSOURCE_GRAPHICSRENDER_H
