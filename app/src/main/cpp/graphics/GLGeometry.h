#ifndef SCREENCASTSOURCE_GLGEOMETRY_H
#define SCREENCASTSOURCE_GLGEOMETRY_H


struct ProgramAttribute {
    unsigned int index;
    int size;
    unsigned int type;
    bool normalized;
    int stride;
    int offset;
};

class GLGeometry {

public:
    GLGeometry();
    ~GLGeometry();
    void create(ProgramAttribute* pAttribs, int nAttribs, unsigned int* pIndices, int nIndices, const void* pVertices, int nVertices);
    void destroy();
    void submit() const;
    void submit(unsigned int sampler, unsigned int texture, unsigned int type) const;

private:
    unsigned int mVAO;
    unsigned int mVBO;
    unsigned int mEBO;

    int mVertexCount;
    int mIndexCount;
};


#endif //SCREENCASTSOURCE_GLGEOMETRY_H
