#ifndef SCREENCASTSOURCE_GLSHADER_H
#define SCREENCASTSOURCE_GLSHADER_H

#include <GLES3/gl3.h>
#include <EGL/egl.h>
#include <EGL/eglext.h>
#include <GLES2/gl2ext.h>
#include <string>
#include <iostream>
#include <unordered_map>

class GLShader {
public:
    GLShader(const char *vertexSrc, const char *fragmentSrc);

    ~GLShader();

    void use();

    void disuse();

    void setBool(const std::string &name, bool value);

    void setInt(const std::string &name, int value);

    void setFloat(const std::string &name, float value);

    void setVec2(const std::string &name, float x, float y);

    void setVec2(const std::string &name, const GLfloat *value);

    void setVec3(const std::string &name, float x, float y, float z);

    void setVec4(const std::string &name, float x, float y, float z, float w);

    void setVec4(const std::string &name, const GLfloat *value);

    void setMat4(const std::string &name, const float *mat);

    void setSampler(const std::string name, unsigned int target, unsigned int texture);

private:
    struct Uniform {
        unsigned int location;
        unsigned int samplerIndex;
        GLenum type;
        std::string name;
    };

    unsigned int ID;
    int mSamplerIndex;
    std::unordered_map<std::string, Uniform> mUniformMap;

private:
    void checkCompileErrors(unsigned int shader, std::string type);

    void generateName2UniformMap();

    bool isSampleUniform(const Uniform &uniform);

    bool isUniformExist(const std::string name) const;
};


#endif // SCREENCASTSOURCE_GLSHADER_H
