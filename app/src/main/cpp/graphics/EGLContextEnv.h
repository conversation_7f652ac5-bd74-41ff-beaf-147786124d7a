#ifndef SCREENCASTSOURCE_EGLCONTEXTENV_H
#define SCREENCASTSOURCE_EGLCONTEXTENV_H

#include <EGL/egl.h>
#include <EGL/eglext.h>

class EGLContextEnv
{
public:
    EGLContextEnv();
    ~EGLContextEnv();

    bool initialize(ANativeWindow *nativeWindow);
    EGLSurface createSurface(ANativeWindow *nativeWindow);
    EGLSurface getSurface();
    bool makeCurrent(EGLSurface surface);
    bool makeCurrent();
    bool swapBuffers(EGLSurface surface);
    bool swapBuffers();
    void clear();
    bool presentationTimeANDROID(EGLSurface surface, long timestamp);
    bool presentationTimeANDROID(long timestamp);

    EGLDisplay display() const { return mDisplay; }
    EGLContext context() const { return mContext; }
    EGLConfig config() const { return mConfig; }

private:
    const char *errorToString(const EGLint error);

private:
    EGLint mMajorVersion;
    EGLint mMinorVersion;
    EGLDisplay mDisplay = EGL_NO_DISPLAY;
    EGLConfig mConfig;
    EGLContext mContext = EGL_NO_CONTEXT;
    EGLSurface mSurface = EGL_NO_SURFACE;
};

#endif // SCREENCASTSOURCE_EGLCONTEXTENV_H