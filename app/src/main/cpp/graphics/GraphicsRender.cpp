#include "GraphicsRender.h"
#include "GLRenderExt.h"

// #define __COPY_CPU_TO_GPU__

const char *vertexShader = R"(
#version 320 es

layout (location = 0) in vec2 aPos;
layout (location = 1) in vec2 aTexCoord;

out vec2 TexCoord;

void main()
{
    gl_Position = vec4(aPos.x, aPos.y, 0.0, 1.0);
    TexCoord = aTexCoord;
}
)";

#ifdef __COPY_CPU_TO_GPU__
const char *fragmentShader = R"(
#version 320 es
#extension GL_OES_EGL_image_external_essl3: require
precision highp float;

out highp vec4 FragColor;
in vec2 TexCoord;
uniform highp sampler2D srcTex;

const lowp vec3 offset = vec3(-0.0625, -0.5, -0.5);
const mediump mat3 converter = mat3(
1.164, 1.164, 1.164,
0.000, -0.391, 2.018,
1.596, -0.813, 0.000
);

void main()
{
    vec3 yuvColor;
    vec2 uv = vec2(TexCoord.x, 1.0 - TexCoord.y);
    ivec2 TexSize = textureSize(srcTex, 0);
    float texCoordX = uv.x * float(TexSize.x) * 2.0;

    if (int(texCoordX) % 2 == 1) {
        yuvColor.x = texture(srcTex, uv).w;
    } else {
        yuvColor.x = texture(srcTex, uv).y;
    }

    yuvColor.y = texture(srcTex, uv).x;// color u
    yuvColor.z = texture(srcTex, uv).z;// color v

    vec3 rgbColor;
    yuvColor += offset;
    rgbColor = converter * yuvColor;
    FragColor = vec4(rgbColor, 1.0);
}
)";

#else

const char *fragmentShader = R"(
#version 320 es
#extension GL_OES_EGL_image_external_essl3: require
precision highp float;

out highp vec4 FragColor;
in vec2 TexCoord;
uniform highp samplerExternalOES srcTex;

const lowp vec3 offset = vec3(-0.0625, -0.5, -0.5);
const mediump mat3 converter = mat3(
1.164, 1.164, 1.164,
0.000, -0.391, 2.018,
1.596, -0.813, 0.000
);

void main()
{
    vec3 yuvColor;
    vec2 uv = vec2(TexCoord.x, 1.0 - TexCoord.y);
    ivec2 TexSize = textureSize(srcTex, 0);
    float texCoordX = uv.x * float(TexSize.x) * 2.0;

    if (int(texCoordX) % 2 == 1) {
        yuvColor.x = texture(srcTex, uv).w;
    } else {
        yuvColor.x = texture(srcTex, uv).y;
    }

    yuvColor.y = texture(srcTex, uv).x;// color u
    yuvColor.z = texture(srcTex, uv).z;// color v

    vec3 rgbColor;
    yuvColor += offset;
    rgbColor = converter * yuvColor;
    FragColor = vec4(rgbColor, 1.0);
}
)";
#endif

GraphicsRender::GraphicsRender(int id) {
    mId = id;
    mWidth = 0;
    mHeight = 0;
    mTexHandle = 0;
    glext::initializeRenderExtensions();
}

GraphicsRender::~GraphicsRender() = default;

void GraphicsRender::create() {
    createTexture();
    createShader();
    createMesh();
}

void GraphicsRender::destroy() {
    destroyTexture();
    destroyShader();
    destroyMesh();
}

bool GraphicsRender::setTexture(void *buffer) const {
    if (buffer == nullptr) {
        LOGE("the buffer is empty");
        return false;
    }

    glActiveTexture(GL_TEXTURE0 + mId);
    glBindTexture(GL_TEXTURE_2D, mTexHandle);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, mWidth / 2, mHeight, 0, GL_RGBA, GL_UNSIGNED_BYTE,
                 buffer);

    return true;
}

bool GraphicsRender::setTexture(AHardwareBuffer *aHardwareBuffer) {
    if (aHardwareBuffer == nullptr) {
        LOGE("the hardware buffer is empty");
        return false;
    }

    auto iter = mEGLImageMap.find(reinterpret_cast<uintptr_t>(aHardwareBuffer));
    if (iter == mEGLImageMap.end()) {
        GLuint textureID;
        glGenTextures(1, &textureID);
        glActiveTexture(GL_TEXTURE0 + mId);
        glBindTexture(GL_TEXTURE_EXTERNAL_OES, textureID);

        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);

        EGLClientBuffer clientBuffer = glext::eglGetNativeClientBufferANDROID(aHardwareBuffer);

        if (clientBuffer == nullptr) {
            glDeleteTextures(1, &textureID);
            LOGE("get native client buffer failed");
            return false;
        }

        EGLint attrs[] = {
                EGL_IMAGE_PRESERVED_KHR,
                EGL_TRUE,
                EGL_NONE,
                EGL_NONE,
                EGL_NONE,
        };
        EGLenum source = EGL_NATIVE_BUFFER_ANDROID;
        EGLImage eglImage = glext::eglCreateImageKHR(eglGetDisplay(EGL_DEFAULT_DISPLAY), EGL_NO_CONTEXT,
                                                     source,
                                                     clientBuffer, attrs);

        if (EGL_NO_IMAGE_KHR == eglImage) {
            glDeleteTextures(1, &textureID);
            LOGE("eglImage is NULL");
            return false;
        }
        glext::glEGLImageTargetTexture2DOES(GL_TEXTURE_EXTERNAL_OES, eglImage);

        mEGLImageMap.insert(
                std::make_pair(reinterpret_cast<uintptr_t>(aHardwareBuffer), std::make_pair(textureID, eglImage)));
        mTexHandle = textureID;
        glBindTexture(GL_TEXTURE_EXTERNAL_OES, GL_NONE);
    } else {
        mTexHandle = iter->second.first;
    }

    return true;
}

void GraphicsRender::render() {
    glClearColor(0.0, 1.0, 0.0, 1.0);
    glClear(GL_COLOR_BUFFER_BIT);

    glViewport(0, 0, mWidth, mHeight);

#ifdef __COPY_CPU_TO_GPU__
    mGeometry->submit(mId, mTexHandle, GL_TEXTURE_2D);
#else
    mGeometry->submit(mId, mTexHandle, GL_TEXTURE_EXTERNAL_OES);
#endif
}

void GraphicsRender::createTexture() {
#if defined(__COPY_CPU_TO_GPU__)
    glGenTextures(1, &mTexHandle);
    glActiveTexture(GL_TEXTURE0 + mId);
    glBindTexture(GL_TEXTURE_2D, mTexHandle);

    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);

    glBindTexture(GL_TEXTURE_2D, GL_NONE);
#endif
}

void GraphicsRender::destroyTexture() {
#if defined(__COPY_CPU_TO_GPU__)
    glDeleteTextures(1, &mTexHandle);
#else
    if (mEGLImageMap.empty()) return;

    for (auto &pair: mEGLImageMap) {
        glBindTexture(GL_TEXTURE_EXTERNAL_OES, pair.second.first);
        glext::glEGLImageTargetTexture2DOES(GL_TEXTURE_EXTERNAL_OES, NULL);

        if (pair.second.second != EGL_NO_IMAGE_KHR) {
            EGLBoolean status = glext::eglDestroyImageKHR(eglGetDisplay(EGL_DEFAULT_DISPLAY), pair.second.second);
            if (status != EGL_TRUE) {
                LOGE("destroyImage failed: %d)\n", status);
            }
        }

        glDeleteTextures(1, &pair.second.first);
    }

    mEGLImageMap.clear();
#endif
}

void GraphicsRender::createShader() {
    mShader = std::make_shared<GLShader>(vertexShader, fragmentShader);
    mShader->use();
    mShader->setInt("srcTex", mId);
}

void GraphicsRender::destroyShader() {
    if (mShader != nullptr) {
        mShader.reset();
    }
}

void GraphicsRender::createMesh() {
    // Quad Vertex buffer
    float vertices[] = {-1, -1,  0,  0.2f,
                         1, -1,  1,  0.2f,
                         1,  1,  1,  1,
                        -1,  1,  0,  1
    };

    // Quad index buffer
    unsigned int indices[] = {0, 1, 3,
                              1, 2, 3
    };

    mGeometry = std::make_shared<GLGeometry>();

    ProgramAttribute attribs[2];

    attribs[0].index = 0;
    attribs[0].size = 2;
    attribs[0].type = GL_FLOAT;
    attribs[0].normalized = GL_FALSE;
    attribs[0].stride = 4 * sizeof(float);
    attribs[0].offset = 0;

    attribs[1].index = 1;
    attribs[1].size = 2;
    attribs[1].type = GL_FLOAT;
    attribs[1].normalized = GL_FALSE;
    attribs[1].stride = 4 * sizeof(float);
    attribs[1].offset = 2 * sizeof(float);

    mGeometry->create(&attribs[0], 2, indices, sizeof(indices), vertices, sizeof(vertices));
}

void GraphicsRender::destroyMesh() {
    if (mGeometry != nullptr) {
        mGeometry->destroy();
        mGeometry.reset();
    }
}

void GraphicsRender::setSize(int width, int height) {
    mWidth = width;
    mHeight = height;
}

