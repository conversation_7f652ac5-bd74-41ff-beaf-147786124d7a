#ifndef SCREENCASTSOURCE_GLRENDEREXT_H
#define SCREENCASTSOURCE_GLRENDEREXT_H

#include <EGL/egl.h>
#include <EGL/eglext.h>
#include <GLES3/gl32.h>
#include <GLES3/gl3ext.h>

#define GL_SAMPLER_EXTERNAL_2D_Y2Y_EXT 0x8BE7

// These are no longer defined in GL3Ext.h
#ifndef GL_TEXTURE_EXTERNAL_OES
#define GL_TEXTURE_EXTERNAL_OES 0x8D65
#endif

#ifndef GL_SAMPLER_EXTERNAL_OES
#define GL_SAMPLER_EXTERNAL_OES 0x8D66
#endif

#ifndef GL_APIENTRYP
#define GL_APIENTRYP GL_APIENTRY*
#endif

// From GLES2/gl2ext.h
typedef void(GL_APIENTRYP PFNGLSTARTTILINGQCOMPROC)(GLuint x, GLuint y, GLuint width, GLuint height,
                                                    GLbitfield preserveMask);

typedef void(GL_APIENTRYP PFNGLENDTILINGQCOMPROC)(GLbitfield preserveMask);

typedef void *GLeglImageOES;

typedef void(GL_APIENTRYP PFNGLEGLIMAGETARGETTEXTURE2DOESPROC)(GLenum target, GLeglImageOES image);

typedef void(GL_APIENTRYP PFNGLTEXTUREVIEWOESPROC)(GLuint texture, GLenum target, GLuint origtexture,
                                                   GLenum internalformat, GLuint minlevel,
                                                   GLuint numlevels, GLuint minlayer, GLuint numlayers);

// From gl3extQCOM.h
typedef void(GL_APIENTRYP PFNGLGENMEMORYOBJECTSKHR)(GLsizei n, GLuint *memoryObjects);

typedef void(GL_APIENTRYP PFNGLDELETEMEMORYOBJECTSKHR)(GLsizei n, const GLuint *memoryObjects);

typedef void(GL_APIENTRYP PFNGLIMPORTMEMORYFDKHR)(GLuint memory, GLuint64 size, GLenum handleType, GLint fd);

typedef void(GL_APIENTRYP PFNGLTEXSTORAGEMEM2DKHR)(GLenum target, GLsizei levels, GLenum internalFormat, GLsizei width,
                                                   GLsizei height, GLuint memory,
                                                   GLuint64 offset);

// For later
typedef void(GL_APIENTRYP PFNGLCREATEMEMORYOBJECTSEXT)(GLsizei n, GLuint *memoryObjects);

typedef void(GL_APIENTRYP PFNGLDELETEMEMORYOBJECTSEXT)(GLsizei n, const GLuint *memoryObjects);

typedef void(GL_APIENTRYP PFNGLIMPORTMEMORYFDEXT)(GLuint memory, GLuint64 size, GLenum handleType, GLint fd);

typedef void(GL_APIENTRYP PFNGLTEXSTORAGEMEM2DEXT)(GLenum target, GLsizei levels, GLenum internalFormat, GLsizei width,
                                                   GLsizei height, GLuint memory,
                                                   GLuint64 offset);

typedef void(EGLAPIENTRYP PFNEGLIMAGETARGETTEXSTORAGEEXTPROC)(GLenum target, GLeglImageOES image,
                                                              const int *attrib_list);

struct AHardwareBuffer;

typedef EGLClientBuffer(EGLAPIENTRYP PFNEGLGETNATIVECLIENTBUFFERANDROIDPROC)(const struct AHardwareBuffer *buffer);

typedef void(GL_APIENTRYP PFNGLTEXSTORAGEMEM3DEXTPROC)(GLenum target, GLsizei levels, GLenum internalFormat,
                                                       GLsizei width, GLsizei height,
                                                       GLsizei depth, GLuint memory, GLuint64 offset);

typedef EGLBoolean(EGLAPIENTRYP PFNEGLPRESENTATIONTTIMEANDROIDROC)(EGLDisplay, EGLSurface, EGLnsecsANDROID);


namespace glext {
    void *initExtension(const char *extName, const char *alternateExtName = NULL);

    bool initializeRenderExtensions();

    extern PFNEGLCREATESYNCKHRPROC eglCreateSyncKHR;
    extern PFNEGLDESTROYSYNCKHRPROC eglDestroySyncKHR;
    extern PFNEGLCLIENTWAITSYNCKHRPROC eglClientWaitSyncKHR;
    extern PFNEGLGETSYNCATTRIBKHRPROC eglGetSyncAttribKHR;
    extern PFNGLSTARTTILINGQCOMPROC glStartTilingQCOM;
    extern PFNGLENDTILINGQCOMPROC glEndTilingQCOM;
    extern PFNGLEGLIMAGETARGETTEXTURE2DOESPROC glEGLImageTargetTexture2DOES;
    extern PFNGLGENMEMORYOBJECTSKHR glGenMemoryObjectsKHR;
    extern PFNGLIMPORTMEMORYFDKHR glImportMemoryFdKHR;
    extern PFNGLTEXSTORAGEMEM2DKHR glTexStorageMem2DKHR;
    extern PFNGLDELETEMEMORYOBJECTSKHR glDeleteMemoryObjectsKHR;
    extern PFNEGLCREATEIMAGEKHRPROC eglCreateImageKHR;
    extern PFNEGLDESTROYIMAGEKHRPROC eglDestroyImageKHR;

// For later
    extern PFNGLCREATEMEMORYOBJECTSEXT glCreateMemoryObjectsEXT;
    extern PFNGLDELETEMEMORYOBJECTSEXT glDeleteMemoryObjectsEXT;
    extern PFNGLIMPORTMEMORYFDEXT glImportMemoryFdEXT;
    extern PFNGLTEXSTORAGEMEM2DEXT glTexStorageMem2DEXT;
    extern PFNEGLIMAGETARGETTEXSTORAGEEXTPROC eglImageTargetTexStorageEXT;
    extern PFNGLTEXTUREVIEWOESPROC glTextureViewOES;
    extern PFNEGLWAITSYNCKHRPROC eglWaitSyncKHR;
    extern PFNGLTEXSTORAGEMEM3DEXTPROC glTexStorageMem3DEXT;
    extern PFNEGLGETNATIVECLIENTBUFFERANDROIDPROC eglGetNativeClientBufferANDROID;
    extern PFNEGLPRESENTATIONTTIMEANDROIDROC eglPresentationTimeANDROID;

}  // End namespace VST


#endif //SCREENCASTSOURCE_GLRENDEREXT_H
