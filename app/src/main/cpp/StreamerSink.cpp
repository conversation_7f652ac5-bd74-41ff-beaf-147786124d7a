#include "StreamerSink.h"

StreamerSink::StreamerSink() {
    mRunning = false;
    mConnected = false;
    mFocused = true;
    mMediaPlayer = nullptr;
    mCallback = nullptr;
}

StreamerSink::~StreamerSink() = default;

void StreamerSink::configure(StreamerSink::Config &config) {
    mConfig = config;
}

void StreamerSink::start() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mRunning) {
        LOGW("StreamerSink has been started");
        return;
    }
    mRunning = true;
    LOGI("[%d]StreamerSink start", mConfig.id);

    mMediaPlayer = std::make_shared<MediaPlayer>();
    if(mConfig.surfaceTexture != nullptr) {
        mMediaPlayer->setSurfaceTexture(mConfig.surfaceTexture);
    }
    mMediaPlayer->setWindow(mConfig.window);
    mMediaPlayer->configure(mConfig.player);
    mMediaPlayer->setCallback(this);

    mMediaPlayer->start();
    mMediaPlayer->setFocused(mFocused);
    LOGI("[%d]StreamerSink started", mConfig.id);
}

void StreamerSink::stop() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        LOGW("[%d]StreamerSink has been stopped", mConfig.id);
        return;
    }
    mRunning = false;
    LOGI("[%d]StreamerSink stop", mConfig.id);

    mMediaPlayer->stop();
    mConnected = false;
    LOGI("[%d]StreamerSink stopped", mConfig.id);
}

bool StreamerSink::isRunning() const {
    std::lock_guard<std::mutex> lock(mMutex);
    return mRunning;
}

bool StreamerSink::isConnected() const {
    return mMediaPlayer != nullptr && (mMediaPlayer->isVideoConnected() || mMediaPlayer->isAudioConnected());
}

void StreamerSink::setVideoFrameDropThreshold(int threshold) {
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setVideoFrameDropThreshold(threshold);
    }
}

void StreamerSink::setAudioFrameDropThreshold(int threshold) {
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setAudioFrameDropThreshold(threshold);
    }
}

void StreamerSink::setFocused(bool focused) {
    std::lock_guard<std::mutex> lock(mMutex);
    mFocused = focused;
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setFocused(focused);
    }

    LOGI("[%d]set StreamerSink focused:%d", mConfig.id, static_cast<int>(focused));
}

void StreamerSink::onFrameAvailable() {
    if (mMediaPlayer != nullptr) mMediaPlayer->onFrameAvailable();
}

void StreamerSink::clearAvailable() {
    if (mMediaPlayer != nullptr) mMediaPlayer->clearAvailable();
}

void StreamerSink::setCallback(StreamerSink::Callback *callback) {
    mCallback = callback;
}

void StreamerSink::onVideoOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mCallback != nullptr) {
        int width, height;
        AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_WIDTH, &width);
        AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_HEIGHT, &height);

        mCallback->onVideoSizeChanged(width, height);
    }
}

void StreamerSink::onVideoConnectionChanged(bool connected) {
    LOGI("[%d]on video connection changed, value:%d", mConfig.id, connected);
    bool conn = StreamerSink::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}

void StreamerSink::onAudioOutputFormatChanged(AMediaFormat *mediaFormat) {
}

void StreamerSink::onAudioConnectionChanged(bool connected) {
    LOGI("[%d]on audio connection changed, value:%d", mConfig.id, connected);
    bool conn = StreamerSink::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}
