#ifndef SCREENCASTSINK_StreamerSink_H
#define SCREENCASTSINK_StreamerSink_H


#include <android/surface_texture.h>
#include "media/VideoPlayer.h"
#include "media/AudioPlayer.h"
#include "Common.h"
#include "media/MediaPlayer.h"

class StreamerSink : MediaPlayer::Callback {
public:
    class Config {
    public:
        int id;
        jobject jSurfaceTexture;
        ASurfaceTexture *surfaceTexture;
        ANativeWindow *window;
        Player::Config player;
    };

    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onVideoSizeChanged(int width, int height) = 0;

        virtual void onConnectionChanged(bool connected) = 0;
    };

public:
    StreamerSink();

    ~StreamerSink() override;

    void configure(StreamerSink::Config &config);

    void start();

    void stop();

    bool isRunning() const;

    bool isConnected() const;

    void setVideoFrameDropThreshold(int threshold);

    void setAudioFrameDropThreshold(int threshold);

    void setFocused(bool focused);

    void onFrameAvailable();

    void clearAvailable();

    void setCallback(StreamerSink::Callback *callback);

protected:
    void onVideoOutputFormatChanged(AMediaFormat *mediaFormat) override;

    void onVideoConnectionChanged(bool connected) override;

    void onAudioOutputFormatChanged(AMediaFormat *mediaFormat) override;

    void onAudioConnectionChanged(bool connected) override;

private:
    bool mRunning;
    bool mConnected;
    bool mFocused;
    mutable std::mutex mMutex;
    StreamerSink::Config mConfig;
    std::shared_ptr<MediaPlayer> mMediaPlayer;
    StreamerSink::Callback *mCallback;
};


#endif //SCREENCASTSINK_StreamerSink_H
