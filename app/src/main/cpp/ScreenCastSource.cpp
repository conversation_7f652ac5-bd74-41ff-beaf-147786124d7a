#include "ScreenCastSource.h"
#include "CameraDef.h"
#include "Common.h"
#include "CameraManager.h"
#include <algorithm>
#include "utils/Statistics.hpp"
#include "utils/Utils.hpp"

ScreenCastSource::ScreenCastSource() {
    InitCameraDevice();

    mDPCallback = new StreamerCallback(DEVICE_ID_DP, this);
    mHDMICallback = new StreamerCallback(DEVICE_ID_HDMI, this);

    mStreamer[DEVICE_ID_DP].setCallback(mDPCallback);
    mStreamer[DEVICE_ID_HDMI].setCallback(mHDMICallback);
}

ScreenCastSource::~ScreenCastSource() {
    mStreamer[DEVICE_ID_DP].setCallback(nullptr);
    mStreamer[DEVICE_ID_HDMI].setCallback(nullptr);

    delete mDPCallback;
    delete mHDMICallback;
}

void ScreenCastSource::init(JNIEnv *env, jobject instance) {
    LOGI("init env:%p instance:%p", env, instance);
    if(env == nullptr || instance == nullptr) {
        return;
    }
    mContext.env = env;
    env->GetJavaVM(&mContext.jvm);
    mContext.instance = env->NewGlobalRef(instance);

    jclass clazz = env->GetObjectClass(instance);
    mNotifyStreamingStateChangedMethod = env->GetMethodID(clazz, "notifyStreamingStateChanged", "(II)V");
}

void ScreenCastSource::clear(JNIEnv *env) {
    LOGW("clear env:%p", env);
    if(env == nullptr) {
        return;
    }
    env->DeleteGlobalRef(mContext.instance);
}

void ScreenCastSource::start(JNIEnv *env, int id, int flag) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);

    if ((flag & STREAMER_FLAG_STREAMING_VIDEO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        int w = getDeviceWidth(value);
        int h = getDeviceHeight(value);
        int fps = getDeviceFrameRate(value);
        if (!isVideoDeviceActivated(value) || w == 0 || h == 0 || fps == 0) {
            LOGW("[%d]video device not activated, w:%d h:%d fps:%d", value, w, h, fps);
            return;
        }

        mConfigs[value].captureConfig.video.width = w;
        mConfigs[value].captureConfig.video.height = h;
        mConfigs[value].captureConfig.video.frameRate = fps;
        LOGI("[%d]configure device video w:%d h:%d frameRate:%d", value,
             mConfigs[value].captureConfig.video.width, mConfigs[value].captureConfig.video.height,
             mConfigs[value].captureConfig.video.frameRate);
    }

    if ((flag & STREAMER_FLAG_STREAMING_AUDIO) != 0 || (flag & STREAMER_FLAG_RECORD_MEDIA) != 0) {
        int sampleRate = getDeviceSampleRate(value);
        if (!isAudioDeviceActivated(value) || sampleRate == 0) {
            LOGW("[%d]audio device not activated, fs:%d", value, sampleRate);
            return;
        }

        mConfigs[value].captureConfig.audio.sampleRate = sampleRate;
        LOGI("[%d]configure audio sampleRate:%d", value, mConfigs[value].captureConfig.audio.sampleRate);
    }

    if ((flag & STREAMER_FLAG_RECORD_MEDIA) != 0) { // for factory test
        mConfigs[value].captureConfig.video.intraRefreshPeriod = 0;
    } else {
        mConfigs[value].captureConfig.video.intraRefreshPeriod = 15;
    }

    mStreamer[value].configure(mConfigs[value]);
    mStreamer[value].setFlag(flag);
    if (flag >= STREAMER_FLAG_RECORD_MEDIA) {
        if (mStreamer[value].isRecording()) {
            LOGW("[%d]streamer is already recording", value);
            return;
        }

        mStreamer[value].startRecording();
    }

    mStreamer[value].start(static_cast<StreamerFlag>(flag));
}

void ScreenCastSource::stop(JNIEnv *env, int id, int flag) {
    LOGI("stop env:%p id:%d flag:%d", env, id, flag);
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);

    if (mStreamer[value].isRecording()) {
        mStreamer[value].stopRecording();
    }

    if ((flag & STREAMER_FLAG_STREAMING_AUDIO) == 0 && (flag & STREAMER_FLAG_STREAMING_VIDEO) == 0) {
        return;
    }

    if (!mStreamer[value].isRunning()) {
        LOGW("[%d]streamer has been stopped", value);
        return;
    }

    mStreamer[value].stop(static_cast<StreamerFlag>(flag));

    if (env != nullptr && !mStreamer[value].isRunning()) {
        env->CallVoidMethod(mContext.instance, mNotifyStreamingStateChangedMethod, value, 0);
    }
}

bool ScreenCastSource::isRunning(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return mStreamer[value].isRunning();
}

bool ScreenCastSource::isStreaming(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return mStreamer[value].isConnected();
}

void ScreenCastSource::setStatisticsLevel(int level) {
    Statistics::Instance().PrintfLevel(level);
}

void ScreenCastSource::saveAudDat(int id, bool save){
    Statistics::Instance().setSaveFile(id,DECODER_AUDIO,save);
}

void ScreenCastSource::saveAudPcm(int id, bool save){
//    Statistics::Instance().PrintfLevel(level);
}

void ScreenCastSource::saveVidDat(int id, bool save){
    Statistics::Instance().setSaveFile(id,DECODER_VIDEO,save);
}

void ScreenCastSource::saveVidYuv(int id, int save){
    SetCameraSaveFile(static_cast<android::yvr::DPDeviceType>(id), save);
}

void ScreenCastSource::configure(JNIEnv *env, int id, jobject configure) {
    LOGI("configure env:%p configure:%p", env, configure);
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    if(env == nullptr && configure == nullptr) {
        mConfigs[value].id = value;
        mConfigs[value].captureConfig.deviceId = 0;
        mConfigs[value].captureConfig.video.bitRate = 30 * 1024 * 1024;
        mConfigs[value].captureConfig.video.frameRate = 60;
        mConfigs[value].captureConfig.video.colorFormat = 0;
        mConfigs[value].captureConfig.video.iFrameInterval = 0xffffffff;
        mConfigs[value].captureConfig.video.intraRefreshPeriod = 15;
        mConfigs[value].captureConfig.video.codec = 2;
        mConfigs[value].captureConfig.video.mimeType = MIME_TYPE_VIDEO_HEVC;
        mConfigs[value].captureConfig.audio.bitRate = 163840;
        mConfigs[value].captureConfig.audio.channelCount = 2;
        mConfigs[value].captureConfig.audio.codec = 2;
        mConfigs[value].captureConfig.audio.mimeType = MIME_TYPE_AUDIO_MP4A_LATM;
        mConfigs[value].connectorConfig.videoIp = "127.0.0.1";
        mConfigs[value].connectorConfig.videoPort = std::to_string(1896);
        mConfigs[value].connectorConfig.videoType = CONNECTION_TYPE_TCP;
        mConfigs[value].connectorConfig.videoMode = CONNECTION_MODE_SERVER;
        mConfigs[value].connectorConfig.audioIp = "127.0.0.1";
        mConfigs[value].connectorConfig.audioPort = std::to_string(1898);
        mConfigs[value].connectorConfig.audioType = CONNECTION_TYPE_TCP;
        mConfigs[value].connectorConfig.audioMode = CONNECTION_MODE_SERVER;
        return;
    }

    jclass clazz = env->GetObjectClass(configure);

    jfieldID fieldID = env->GetFieldID(clazz, "deviceId", "I");
    jint deviceId = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "videoBitRate", "I");
    jint videoBitRate = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "videoFrameRate", "I");
    jint videoFrameRate = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "videoColorFormat", "I");
    jint videoColorFormat = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "videoCodec", "I");
    jint videoCodec = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "audioBitRate", "I");
    jint audioBitRate = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "audioChannelCount", "I");
    jint audioChannelCount = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "audioCodec", "I");
    jint audioCodec = env->GetIntField(configure, fieldID);

    fieldID = env->GetFieldID(clazz, "port", "I");
    jint port = env->GetIntField(configure, fieldID);

    mConfigs[value].id = value;
    mConfigs[value].captureConfig.deviceId = deviceId;
    mConfigs[value].captureConfig.video.bitRate = videoBitRate;
    mConfigs[value].captureConfig.video.frameRate = videoFrameRate;
    mConfigs[value].captureConfig.video.colorFormat = videoColorFormat;
    mConfigs[value].captureConfig.video.iFrameInterval = 0xffffffff;
    mConfigs[value].captureConfig.video.intraRefreshPeriod = 15;
    mConfigs[value].captureConfig.video.codec = videoCodec;
    mConfigs[value].captureConfig.video.mimeType = videoCodec == VIDEO_CODEC_H264 ? MIME_TYPE_VIDEO_AVC
                                                                                  : MIME_TYPE_VIDEO_HEVC;
    mConfigs[value].captureConfig.audio.bitRate = audioBitRate;
    mConfigs[value].captureConfig.audio.channelCount = audioChannelCount;
    mConfigs[value].captureConfig.audio.codec = audioCodec;
    mConfigs[value].captureConfig.audio.mimeType = MIME_TYPE_AUDIO_MP4A_LATM;
    mConfigs[value].connectorConfig.videoIp = "127.0.0.1";
    mConfigs[value].connectorConfig.videoPort = std::to_string(port);
    mConfigs[value].connectorConfig.videoType = CONNECTION_TYPE_TCP;
    mConfigs[value].connectorConfig.videoMode = CONNECTION_MODE_SERVER;
    mConfigs[value].connectorConfig.audioIp = "127.0.0.1";
    mConfigs[value].connectorConfig.audioPort = std::to_string(port + 2);
    mConfigs[value].connectorConfig.audioType = CONNECTION_TYPE_TCP;
    mConfigs[value].connectorConfig.audioMode = CONNECTION_MODE_SERVER;
}

int ScreenCastSource::getDeviceWidth(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return value == DEVICE_ID_DP ? readInteger("/sys/bus/i2c/drivers/lt7911/0-0043/h_active")
                                : readInteger("/sys/bus/i2c/drivers/lt6911/1-002b/h_active");
}

int ScreenCastSource::getDeviceHeight(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return value == DEVICE_ID_DP ? readInteger("/sys/bus/i2c/drivers/lt7911/0-0043/v_active")
                                : readInteger("/sys/bus/i2c/drivers/lt6911/1-002b/v_active");
}

int ScreenCastSource::getDeviceFrameRate(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    if (DEVICE_ID_DP == value) {
        return roundToNearestFps(readInteger("/sys/bus/i2c/drivers/lt7911/0-0043/fps"));
    } else {
        return roundToNearestFps(readInteger("/sys/bus/i2c/drivers/lt6911/1-002b/fps"));
    }
}

//isVideoDeviceActivated
bool ScreenCastSource::isVideoDeviceActivated(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return value == DEVICE_ID_DP ? readInteger("/sys/bus/i2c/drivers/lt7911/0-0043/video_ready") == 1
                                : readInteger("/sys/bus/i2c/drivers/lt6911/1-002b/video_ready") == 1;
}

//isAudioDeviceActivated
bool ScreenCastSource::isAudioDeviceActivated(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    return value == DEVICE_ID_DP ? readInteger("/sys/bus/i2c/devices/0-0043/audio_ready") == 1
                                 : readInteger("/sys/bus/i2c/devices/1-002b/audio_ready") == 1;
}

int ScreenCastSource::getDeviceSampleRate(int id) {
    int value = std::clamp(id, (int) DEVICE_ID_DP, (int) DEVICE_ID_HDMI);
    int sampleRate = value == DEVICE_ID_DP
                     ? roundToNearestSampleRate(readInteger("/sys/bus/i2c/devices/0-0043/audio_fs_value") * 1000)
                     : roundToNearestSampleRate(readInteger("/sys/bus/i2c/devices/1-002b/audio_fs_value") * 1000);

//    if (sampleRate == 0) {
//        LOGE("invalid sample rate, set to 48000");
//        sampleRate = 48000;
//    }

    return sampleRate;
}

void ScreenCastSource::onConnectionChanged(DeviceID id, bool connected) {
    LOGI("[%d]on streamer connection changed, connected:%d, mNotifyStreamingStateChangedMethod:%p", id, connected, mNotifyStreamingStateChangedMethod);
    if(mNotifyStreamingStateChangedMethod == nullptr) {
        return;
    }
    JNIThreadAttacher attacher(mContext.jvm);
    attacher.getEnv()->CallVoidMethod(mContext.instance, mNotifyStreamingStateChangedMethod, (int) id, connected ? 1 : 0);
}

void ScreenCastSource::setCallback(ScreenCastSource::Callback *callback) {
    mCallback = callback;
}

void ScreenCastSource::StreamerCallback::onConnectionChanged(bool connected) {
    mSource->onConnectionChanged(mID, connected);
}

void ScreenCastSource::StreamerCallback::onReceived(const char *data, int size) {
}

void
ScreenCastSource::StreamerCallback::onReceived(const char *data, int size, int mediaType, int width,
                                               int height, void *pStatus) {
    LOGI("ScreenCastSource::StreamerCallback::onReceived size:%d, mediaType:%d", size, mediaType);
    if(mSource->mCallback == nullptr) {
        return;
    }
    mSource->mCallback->onReceived(data, size, mediaType, width, height, pStatus);
}

