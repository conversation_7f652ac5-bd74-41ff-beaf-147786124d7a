#pragma once


#ifndef PFDM_SCREENCAST_CAMERA_DEF_H
#define PFDM_SCREENCAST_CAMERA_DEF_H

#define BUFFER_SIZE (16)

#define DP_DEVICE_0_WIDTH (1920*2)
#define DP_DEVICE_0_HEIGHT (1080)
#define DP_DEVICE_0_FORMAT HAL_PIXEL_FORMAT_RAW10
#define DP_DEVICE_0_BUFFER_FORMAT HAL_PIXEL_FORMAT_RGBA_8888

#define DP_DEVICE_1_WIDTH (2328)
#define DP_DEVICE_1_HEIGHT (1748)
#define DP_DEVICE_1_FORMAT HAL_PIXEL_FORMAT_YCBCR_420_888
#define DP_DEVICE_1_BUFFER_FORMAT DP_DEVICE_1_FORMAT

struct CamSize {
    unsigned int w;
    unsigned int h;
};

struct CameraAHBuffer {
    void* ahbuffer;
    void* handle;
    void* vaddr;
    CamSize imageSize;
    CamSize bufferSize;
    unsigned int size;
};

namespace android {
namespace yvr {

enum DPDeviceType {
    DP_DEVICE_UNKNOWN = -1,
    DP_DEVICE_0 = 0,
    DP_DEVICE_1 = 1,
    DP_DEVICE_MAX = 2,
};


class CameraListener
{
public:
   virtual ~CameraListener() {}
   virtual void onFrameAvailable(CameraAHBuffer &buffer, DPDeviceType type) = 0;
};
}
}

#endif // PFDM_SCREENCAST_CAMERA_DEF_H