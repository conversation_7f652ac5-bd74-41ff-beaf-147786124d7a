/**
 * @file CameraModuleManager.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2023-05-31
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#define LOG_TAG "DPCameraDevice"
#include <android/hidl/manager/1.2/IServiceManager.h>
#include <hidl/ServiceManagement.h>
#include "CameraModuleManager.h"

// using namespace android;
using ::android::hardware::configureRpcThreadpool;
using ::android::hardware::defaultServiceManager1_2;
using ::android::hardware::hidl_string;
using ::android::hardware::hidl_vec;
using ::android::hardware::Return;
using ::android::hardware::camera::common::V1_0::Status;
using ::android::hardware::camera::device::V3_2::ICameraDevice;
using ::android::hardware::camera::provider::V2_4::ICameraProvider;

namespace android {
namespace yvr {

const VendorTagName RCV_TAG = {"org.codeaurora.qcamera3.sessionParameters", "overrideResourceCostValidation"};
uint32_t vTAGRCV = 0;

CameraModuleManager::CameraModuleManager() {
    /*add hwbinder thread*/
    mInitSuccess = false;
    configureRpcThreadpool(5, /*willjoin*/ false);
    mCameraStarted[DP_DEVICE_0] = false;
    mCameraStarted[DP_DEVICE_1] = false;

    mWidth[DP_DEVICE_0] = DP_DEVICE_0_WIDTH>>1;
    mWidth[DP_DEVICE_1] = DP_DEVICE_0_WIDTH>>1;
    mHeight[DP_DEVICE_0] = DP_DEVICE_0_HEIGHT;
    mHeight[DP_DEVICE_1] = DP_DEVICE_0_HEIGHT;
    mSaveFile[DP_DEVICE_0] = false;
    mSaveFile[DP_DEVICE_1] = false;
}

CameraModuleManager::~CameraModuleManager() {}

int CameraModuleManager::initalize() {
    if (mInitSuccess == true) {
        return true;
    }
    
    hidl_vec<hidl_string> serviceslist;
    const std::string cameraProviderName = "legacy/0";
    auto sm = defaultServiceManager1_2();
    if (sm != nullptr) {
        sm->listManifestByInterface(ICameraProvider::descriptor,
                                    [&serviceslist](const hidl_vec<hidl_string>& registered) { serviceslist = registered; });
    } else {
        ALOGE("defaultServiceManager1_2 failed");
    }

    if (serviceslist.size() <= 0) {
        ALOGE("get camera services failed");
        return false;
    }

    for (auto& name : serviceslist) {
        /*only need internal camera provider*/
        if (cameraProviderName.compare(name.c_str()) == 0) {
            mCameraProvider = ICameraProvider::getService(name);
            break;
        }
    }

    if (mCameraProvider == NULL) {
        ALOGE("get camera provider failed");
        return false;
    } else {
        loadVendorTagId();
    }

    mInitSuccess = true;
    return true;
}

hidl_vec<hidl_string> CameraModuleManager::getDeviceNames() {
    size_t count;
    hidl_vec<hidl_string> retList;

    Return<void> ret = mCameraProvider->getCameraIdList([&](auto status, const auto& devices) {
        if (status == Status::OK) {
            count = devices.size();
            retList.resize(count);
            ALOGI("camera count:%zu", count);
            for (size_t i = 0; i < count; i++) {
                retList[i] = devices[i];
                ALOGI("camera name[%zu]:%s", i, devices[i].c_str());
            }
        } else {
            ALOGE("getDeviceNames failed");
        }
    });

    if (!ret.isOk()) {
        ALOGE("getDeviceNames failed");
    }
    return retList;
}

void CameraModuleManager::loadVendorTagId() {
    hidl_vec<hardware::camera::common::V1_0::VendorTagSection> vts;
    Return<void> ret = mCameraProvider->getVendorTags([&](auto s, const auto& vendorTagSecs) {
        if (s == Status::OK) {
            vts = vendorTagSecs;
        }
    });

    if (!ret.isOk()) {
        ALOGE("initVendorTagIds failed");
        return;
    }

    vTAGRCV = getTagId(vts, RCV_TAG);
}

uint32_t CameraModuleManager::getTagId(hidl_vec<hardware::camera::common::V1_0::VendorTagSection>& vts, const VendorTagName& tag) {
    for (size_t i = 0; i < vts.size(); i++) {
        if (strcmp(vts[i].sectionName.c_str(), tag.sectionName) == 0) {
            for (size_t j = 0; j < vts[i].tags.size(); j++) {
                if (strcmp(vts[i].tags[j].tagName.c_str(), tag.tagName) == 0) {
                    return vts[i].tags[j].tagId;
                }
            }
        }
    }
    return 0;
}


sp<ICameraDevice> CameraModuleManager::startDevice(const std::string& name) {
    Status status;
    sp<ICameraDevice> device;
    Return<void> ret;

    ret = mCameraProvider->getCameraDeviceInterface_V3_x(name, [&status, &device](Status s, sp<ICameraDevice> provider) {
        status = s;
        device = provider;
    });
    if (!ret.isOk() || status != Status::OK) {
        ALOGE("startDevice %s failed", name.c_str());
    }

    return device;
}

DPDeviceType CameraModuleManager::getDPDeviceType(DPDeviceType deviceType, const char* name, sp<ICameraDevice> device) {
    camera_metadata_t* metadata_p = nullptr;
    camera_metadata_ro_entry entry;
    int32_t rc = 0;
    int32_t max_width = 0;
    int32_t max_height = 0;

    Return<void> ret = device->getCameraCharacteristics([&](Status s, CameraMetadata metadata) {
        metadata_p = clone_camera_metadata(reinterpret_cast<const camera_metadata_t*>(metadata.data()));
        if (s != Status::OK) {
            ALOGE("get camera characteristics failed, name:%s", name);
        }
    });

    if (!ret.isOk()) {
        ALOGE("get camera characteristics not ok, name:%s", name);
    }

    rc = find_camera_metadata_ro_entry(metadata_p, ANDROID_SCALER_AVAILABLE_STREAM_CONFIGURATIONS, &entry);
    if (rc != 0) {
        ALOGE("get support output size failed, name:%s", name);
        return DP_DEVICE_UNKNOWN;
    }

    if (metadata_p == nullptr) {
        ALOGE("get metadata failed, name:%s", name);
        return DP_DEVICE_UNKNOWN;
    }

    bool bEnableResolve = false;
    int width = mWidth[deviceType] << 1;
    int height = mHeight[deviceType];

    for (size_t i = 0; i < entry.count; i += 4) {
        if (ANDROID_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT == entry.data.i32[i + 3]) {
            if (entry.data.i32[i + 1] == width && entry.data.i32[i + 2] == height)
            {
                bEnableResolve = true;
            }
            
            if (entry.data.i32[i + 1] > max_width) {
                max_width = entry.data.i32[i + 1];
                max_height = entry.data.i32[i + 2];
            }
        }
    }

    free_camera_metadata(metadata_p);
    ALOGE("[%d]device, width:%d, height:%d; set: %dx%d; enable: %d", deviceType,
        max_width, max_height, width, height, bEnableResolve);

    if (bEnableResolve == true) {
        ALOGI("[%d]found dp device, name:%s", deviceType, name);
        return deviceType;
    
    } else {
        return DP_DEVICE_UNKNOWN;
    }
}

int8_t CameraModuleManager::startModule(DPDeviceType deviceType) {
    if (initalize() == false) {
        ALOGE("initialize CameraProviderManager failed");
        return false;
    }
    std::unique_lock<std::mutex> l(mLock);

    if (mCameraStarted[deviceType] == true) {
        ALOGI("camera is already started");
        return true;
    }

    hidl_vec<hidl_string> devices = getDeviceNames();
    for (int i = deviceType; i < (int)devices.size(); i++) {
        hidl_string name = devices[i];
        const char* str = name.c_str();

        sp<ICameraDevice> device = startDevice(name);
        DPDeviceType currDeviceType = getDPDeviceType(deviceType, str, device);

        if (currDeviceType == deviceType) {
            sp<CameraDevice> cameradevice = new CameraDevice(currDeviceType, device, mWidth[deviceType], 
                                                mHeight[deviceType], mSaveFile[deviceType]);
            if (cameradevice->configCameraStream() == true) {
                mCameraDevices[deviceType].add(name, cameradevice);
                ALOGI("open device success: %s", str);
            } else {
                ALOGE("open device failed");
            }
            break;
        }
    }

    if (mCameraDevices[deviceType].size() <= 0) {
        return false;
    }

    for (int i = 0; i < (int)mCameraDevices[deviceType].size(); i++) {
        mCameraDevices[deviceType].valueAt(i)->startCamera();
    }
    mCameraStarted[deviceType] = true;
    return true;
}

int8_t CameraModuleManager::startModule(DPDeviceType deviceType, CameraListener *listener) {
    if (initalize() == false) {
        ALOGE("initialize CameraProviderManager failed");
        return false;
    }
    std::unique_lock <std::mutex> l(mLock);

    if (mCameraStarted[deviceType] == true) {
        ALOGI("camera is already started");
        return true;
    }

    hidl_vec<hidl_string> devices = getDeviceNames();
    for (int i = deviceType; i < (int) devices.size(); i++) {
        hidl_string name = devices[i];
        const char *str = name.c_str();
        ALOGI("camera %s to start",str);

        sp <ICameraDevice> device = startDevice(name);
        DPDeviceType currDeviceType = getDPDeviceType(deviceType, str, device);

        if (currDeviceType == deviceType) {
            sp <CameraDevice> cameradevice = new CameraDevice(currDeviceType, device, mWidth[deviceType], 
                                                mHeight[deviceType], mSaveFile[deviceType]);
            if (listener != nullptr) {
                ALOGI("set camera listener");
                cameradevice->setListener(listener);
            }

            if (cameradevice->configCameraStream() == true) {
                mCameraDevices[deviceType].add(name, cameradevice);
                ALOGI("open device success: %s", str);
            } else {
                ALOGE("open device failed");
            }
            break;
        }
    }

    if (mCameraDevices[deviceType].size() <= 0) {
        return false;
    }

    for (int i = 0; i < (int) mCameraDevices[deviceType].size(); i++) {
        mCameraDevices[deviceType].valueAt(i)->startCamera();
    }
    mCameraStarted[deviceType] = true;
    return true;
}

int8_t CameraModuleManager::stopModule(DPDeviceType deviceType) {
    std::unique_lock<std::mutex> l(mLock);

    if (mCameraStarted[deviceType] == false) {
        ALOGI("camera is already stopped");
        return true;
    }

    for (int i = 0; i < (int)mCameraDevices[deviceType].size(); i++) {
        if (mCameraDevices[deviceType].valueAt(i)->getDeviceType() == deviceType) {
            mCameraDevices[deviceType].valueAt(i)->stopCamera();
            break;
        }
    }

    mCameraDevices[deviceType].clear();
    mCameraStarted[deviceType] = false;
    return true;
}

void CameraModuleManager::SetResolve(DPDeviceType deviceType,int width, int height){
    ALOGI("[%d]set resolve %dx%d", deviceType, width, height);
    mWidth[deviceType] = width;
    mHeight[deviceType] = height;
}

void CameraModuleManager::SaveFile(DPDeviceType deviceType, bool bsave){
    ALOGI("[%d]set SaveFile %d", deviceType, bsave);
    mSaveFile[deviceType] = bsave;
}

}  // namespace yvr
}  // namespace android
