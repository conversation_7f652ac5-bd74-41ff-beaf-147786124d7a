/**
 * @file CameraBufferManager.cpp
 * <AUTHOR> (huang<PERSON><PERSON>@yvr.com)
 * @brief 
 * @version 0.1
 * @date 2023-09-11
 * 
 * @copyright Copyright (c) 2023
 * 
 */

#define LOG_TAG "CameraBufferManager"

#include <android/log.h>
#include <ui/GraphicBuffer.h>
#include "gr_priv_handle.h"
#include "CameraBufferManager.h"

CameraBufferManager::CameraBufferManager() {
    mAllocatedBuffers = NULL;
    mQueueSize = 0;
    mIsAvailable = false;
}

CameraBufferManager::~CameraBufferManager() { freeBuffers(); }

void CameraBufferManager::allocateBuffer(CameraBufferDesc& desc) {
    if (desc.queueSize == 0 || desc.streamNum == 0) {
        ALOGE("invalid desc");
        return;
    }

    std::unique_lock<std::mutex> lock(mBufMutex);

    mQueueSize = desc.queueSize;
    mStreamNum = desc.streamNum;
    mAllocatedBuffers = (CameraBufferInfo*)malloc(sizeof(CameraBufferInfo) * mQueueSize);

    for (uint32_t i = 0; i < mQueueSize; i++) {
        mAllocatedBuffers[i].buffers = (CameraAHBuffer*)malloc(sizeof(CameraAHBuffer) * mStreamNum);
        memset(mAllocatedBuffers[i].buffers, 0x00, sizeof(CameraAHBuffer) * mStreamNum);
        mAllocatedBuffers[i].streamNum = mStreamNum;
        mAllocatedBuffers[i].bufferIndex = i;
        mBuffersFree.push_back(&mAllocatedBuffers[i]);
    }

    for (uint32_t i = 0; i < mStreamNum; i++) {
        AHardwareBuffer_Desc adesc;
        memset(&adesc, 0x00, sizeof(AHardwareBuffer_Desc));
        adesc.width = desc.items[i].width;
        adesc.height = desc.items[i].height;
        adesc.format = desc.items[i].format;
        adesc.usage = desc.items[i].usage;
        adesc.layers = 1;

        for (uint32_t j = 0; j < mQueueSize; j++) {
            AHardwareBuffer* buffer = nullptr;
            void* vaddr = nullptr;
            memset(&mAllocatedBuffers[j].buffers[i], 0x00, sizeof(CameraAHBuffer));
            if (AHardwareBuffer_allocate(&adesc, &buffer)) {
                ALOGE("failed to allocate ahb");
            }
            android::GraphicBuffer* gBuffer = reinterpret_cast<android::GraphicBuffer*>(buffer);
            const private_handle_t* hnd = (const private_handle_t*)gBuffer->handle;
            AHardwareBuffer_lock(buffer, android::GraphicBuffer::USAGE_SW_READ_OFTEN, -1, NULL, &vaddr);
            mAllocatedBuffers[j].buffers[i].ahbuffer = buffer;
            mAllocatedBuffers[j].buffers[i].handle = (native_handle_t*)hnd;
            mAllocatedBuffers[j].buffers[i].vaddr = vaddr;
            mAllocatedBuffers[j].buffers[i].size = hnd->size;
            mAllocatedBuffers[j].buffers[i].imageSize.w = adesc.width;
            mAllocatedBuffers[j].buffers[i].imageSize.h = adesc.height;
            mAllocatedBuffers[j].buffers[i].bufferSize.w = hnd->width;
            mAllocatedBuffers[j].buffers[i].bufferSize.h = hnd->height;
        }
    }

    mIsAvailable = true;
}

void CameraBufferManager::freeBuffers(void) {
    std::unique_lock<std::mutex> lock(mBufMutex);

    mBuffersFree.clear();

    if (mAllocatedBuffers != NULL) {
        for (uint32_t i = 0; i < mQueueSize; i++) {
            for (uint32_t j = 0; j < mStreamNum; j++) {
                if (mAllocatedBuffers[i].buffers != NULL) {
                    AHardwareBuffer_unlock((AHardwareBuffer*)mAllocatedBuffers[i].buffers[j].ahbuffer, NULL);
                    AHardwareBuffer_release((AHardwareBuffer*)mAllocatedBuffers[i].buffers[j].ahbuffer);
                    mAllocatedBuffers[i].buffers[j].ahbuffer = NULL;
                    mAllocatedBuffers[i].buffers[j].handle = NULL;
                    mAllocatedBuffers[i].buffers[j].vaddr = nullptr;
                }
            }

            if (mAllocatedBuffers[i].buffers != NULL) {
                free(mAllocatedBuffers[i].buffers);
                mAllocatedBuffers[i].buffers = NULL;
            }
        }

        free(mAllocatedBuffers);
        mAllocatedBuffers = NULL;
    }
    mQueueSize = 0;
    mIsAvailable = false;

    ALOGI("free ahbuffers");
}

CameraBufferInfo* CameraBufferManager::dequeueBuffer() {
    std::unique_lock<std::mutex> lock(mBufMutex);
    if (mBuffersFree.size() == 0) {
        return NULL;
    }
    CameraBufferInfo* buffer = mBuffersFree.front();
    mBuffersFree.pop_front();
    return buffer;
}

void CameraBufferManager::queueBuffer(CameraBufferInfo* buffer) {
    std::unique_lock<std::mutex> lock(mBufMutex);
    mBuffersFree.push_back(buffer);
}

void CameraBufferManager::reInitBufferQueue() {
    std::unique_lock<std::mutex> lock(mBufMutex);

    mBuffersFree.clear();

    for (uint32_t i = 0; i < mQueueSize; i++) {
        mBuffersFree.push_back(&mAllocatedBuffers[i]);
    }
}

bool CameraBufferManager::isAvailable() { return mIsAvailable; }

