/**
 * @file CameraDevice.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2023-05-31
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#pragma once
#include <cutils/properties.h>
#include <hidl/HidlTransportSupport.h>
#include <utils/RefBase.h>
#include <utils/KeyedVector.h>
#include <utils/Thread.h>

#include <CameraMetadata.h>
#include <android/hardware/camera/device/3.5/ICameraDevice.h>
#include <android/hardware/camera/device/3.5/ICameraDeviceCallback.h>
#include <android/hardware/camera/device/3.5/ICameraDeviceSession.h>

#include <fmq/MessageQueue.h>
#include "CameraBufferManager.h"
#include "CameraDef.h"
#include "CapStatistics.hpp"

using namespace ::android::hardware::camera::device;
using ::android::hardware::hidl_string;
using ::android::hardware::hidl_vec;
using ::android::hardware::kSynchronizedReadWrite;
using ::android::hardware::MessageQueue;
using ::android::hardware::Return;
using ::android::hardware::Void;
using ::android::hardware::camera::common::V1_0::Status;
using ::android::hardware::camera::device::V3_2::BufferCache;
using ::android::hardware::camera::device::V3_2::BufferStatus;
using ::android::hardware::camera::device::V3_2::CameraMetadata;
using ::android::hardware::camera::device::V3_2::CaptureRequest;
using ::android::hardware::camera::device::V3_2::CaptureResult;
using ::android::hardware::camera::device::V3_2::ErrorCode;
using ::android::hardware::camera::device::V3_2::HalStreamConfiguration;
using ::android::hardware::camera::device::V3_2::ICameraDevice;
using ::android::hardware::camera::device::V3_2::ICameraDeviceSession;
using ::android::hardware::camera::device::V3_2::MsgType;
using ::android::hardware::camera::device::V3_2::NotifyMsg;
using ::android::hardware::camera::device::V3_2::RequestTemplate;
using ::android::hardware::camera::device::V3_2::StreamBuffer;
using ::android::hardware::camera::device::V3_2::StreamConfiguration;
using ::android::hardware::camera::device::V3_2::StreamConfigurationMode;
using ::android::hardware::camera::device::V3_2::StreamRotation;
using ::android::hardware::camera::device::V3_2::StreamType;
using ::android::hardware::camera::device::V3_4::PhysicalCameraMetadata;
using ::android::hardware::camera::device::V3_5::BufferRequestStatus;
using ::android::hardware::camera::device::V3_5::ICameraDeviceCallback;
using ::android::hardware::camera::device::V3_5::StreamBufferRet;
using ::android::hardware::graphics::common::V1_0::Dataspace;
using ResultMetadataQueue = MessageQueue<uint8_t, kSynchronizedReadWrite>;

namespace android {
namespace yvr {



struct AvailableStream {
    int32_t width;
    int32_t height;
    int32_t format;
};

// In-flight queue for tracking completion of capture requests.
struct InFlightRequest {
    uint64_t bTimestamp;
    int64_t frameNumber;
    int32_t partialResultCount;
    CameraBufferInfo* bufferInfo;

    InFlightRequest() : bTimestamp(0), frameNumber(0), partialResultCount(0) {}
};

class CameraDevice : public virtual RefBase, virtual public V3_5::ICameraDeviceCallback {
 public:
    CameraDevice(DPDeviceType deviceType, sp<ICameraDevice> device, int width, int height, bool bsave);
    ~CameraDevice();
    DPDeviceType getDeviceType();
    void initCameraDevice();
    void allocCameraBuffer();
    void getCameraCharacteristics(sp<ICameraDevice> device, camera_metadata_t** staticMeta);
    bool isUseHalBufManager(camera_metadata_t* metadata);
    Status getJpegBufferSize(camera_metadata_t* metadata, uint32_t* outBufSize);
    void getRequestPartialResultCount(camera_metadata_t* metadata, bool* supportsPartialResults, uint32_t* partialResultCount);
    void createCameraSession(sp<ICameraDevice> device, sp<V3_5::ICameraDeviceCallback> cb, sp<ICameraDeviceSession>* session);
    void initResultMetadataQueue(sp<ICameraDeviceSession> session);
    void processCaptureRequest(void);
    void constructDefaultRequestSettings(sp<ICameraDeviceSession> session, CameraMetadata& settings);
    void getTimestampFromMetadata(CameraMetadata& metadata, uint64_t& btimestamp);
    bool processCaptureResultLocked(const CaptureResult& results, hidl_vec<PhysicalCameraMetadata> physicalCameraMetadata);
    void configurePreviewStream(camera_metadata_t* metadata, const AvailableStream* previewThreshold, sp<ICameraDeviceSession>* session /*out*/,
                                V3_2::Stream* previewStream /*out*/, HalStreamConfiguration* halStreamConfig /*out*/, uint32_t streamConfigCounter);
    Status getAvailableOutputStreams(camera_metadata_t* staticMeta, std::vector<AvailableStream>& outputStreams, const AvailableStream* threshold);
    void fillOutputStreams(camera_metadata_ro_entry_t* entry, std::vector<AvailableStream>& outputStreams, const AvailableStream* threshold,
                           const int32_t availableConfigOutputTag);
    void createStreamConfiguration(const hidl_vec<V3_2::Stream>& streams3_2, StreamConfigurationMode configMode, V3_5::StreamConfiguration* config3_5,
                                   uint32_t jpegBufferSize);
    bool configCameraStream();
    void setTargetFPS(uint32_t fps);
    void setRCVState();
    void setMetaDataEntry(camera_metadata_t *settings, uint32_t tag, const void *data, size_t size);
    bool startCamera();
    void stopCamera();
    void dumpFileTest(InFlightRequest& request);
    void setListener(CameraListener *listener);
    Return<void> processCaptureResult_3_4(const hidl_vec<V3_4::CaptureResult>& results) override;
    Return<void> processCaptureResult(const hidl_vec<V3_2::CaptureResult>& results) override;
    Return<void> notify(const hidl_vec<V3_2::NotifyMsg>& msgs) override;
    Return<void> requestStreamBuffers(const hidl_vec<V3_5::BufferRequest>& bufReqs, requestStreamBuffers_cb _hidl_cb) override;
    Return<void> returnStreamBuffers(const hidl_vec<V3_2::StreamBuffer>& buffers) override;

 private:
    bool convertFromHidl(const CameraMetadata& src, const camera_metadata_t** dst);
    void convertToHidl(const camera_metadata_t* src, CameraMetadata* dst);
    int32_t kPreferredWidth;
    int32_t kPreferredHeight;
    int32_t kPreferredFormat;
    int32_t mBufferWidth;
    int32_t mBufferHeight;
    int32_t mBufferFormat;
    uint64_t mLastTimeStamp;
    std::mutex mLock;  // protecting members below
    DPDeviceType mDeviceType;
    int mFile = 0;
    int mFileNum = 0;
    bool mSaveFile = false;
    int mStreamId;
    std::shared_ptr<ResultMetadataQueue> mResultQueue;
    sp<CameraBufferManager> mBufferManager;
    uint32_t mframeNumber = 1;
    KeyedVector<uint32_t, InFlightRequest> mInflightMap;
    CameraMetadata mMetadataSetting;
    camera_metadata_t* mStaticMetadata = nullptr;
    sp<ICameraDevice> mCameraDevice;
    sp<ICameraDeviceSession> mSession;
    bool mSupportsPartialResults = false;
    uint32_t mPartialResultCount = 0;
   //  std::shared_ptr<Statistics> mStatistics;

    class RequestThread : public android::Thread {
     public:
        RequestThread(sp<CameraDevice> device);
        ~RequestThread();

     protected:
        virtual bool threadLoop();

     private:
        sp<CameraDevice> mCameraDevice;
    };

    sp<RequestThread> mRequestThread;

    CameraListener *mListener = nullptr;
};

}  // namespace yvr
}  // namespace android
