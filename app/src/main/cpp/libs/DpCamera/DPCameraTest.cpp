/**
 * @file YvrCameraTest.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2023-05-31
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#define LOG_TAG "DPCameraTest"
#include <sys/stat.h>
#include "CameraModuleManager.h"
#include <binder/IServiceManager.h>
#include <binder/IPCThreadState.h>
#include <binder/ProcessState.h>

using android::sp;
using android::yvr::CameraModuleManager;
using android::yvr::DP_DEVICE_0;
using android::yvr::DP_DEVICE_1;

static struct sigaction gOrigSigactionINT;
static struct sigaction gOrigSigactionHUP;
static struct sigaction gOrigSigactionStop;
static struct sigaction gOrigSigactionStart;

sp<CameraModuleManager> pCameramodule;

static void signalCatcher(int signum) {
    ALOGI("catch sigint or sighup:%d, exit.", signum);
    switch (signum) {
        case SIGINT:
        case SIGHUP:
            pCameramodule->stopModule(DP_DEVICE_0);
            pCameramodule->stopModule(DP_DEVICE_1);
            sigaction(SIGINT, &gOrigSigactionINT, NULL);
            sigaction(SIGHUP, &gOrigSigactionHUP, NULL);
            break;
        case 40:
            pCameramodule->stopModule(DP_DEVICE_0);
            pCameramodule->stopModule(DP_DEVICE_1);
            break;
        case 41:
            pCameramodule->startModule(DP_DEVICE_0);
            pCameramodule->startModule(DP_DEVICE_1);
            break;
        default:
            abort();
            break;
    }
}

static int configureSignals() {
    struct sigaction act;
    memset(&act, 0, sizeof(act));
    act.sa_handler = signalCatcher;
    if (sigaction(SIGINT, &act, &gOrigSigactionINT) != 0) {
        int err = -errno;
        ALOGE("Unable to configure SIGINT handler: %s\n", strerror(errno));
        return err;
    }
    if (sigaction(SIGHUP, &act, &gOrigSigactionHUP) != 0) {
        int err = -errno;
        ALOGE("Unable to configure SIGHUP handler: %s\n", strerror(errno));
        return err;
    }

    if (sigaction(40, &act, &gOrigSigactionStop) != 0) {
        int err = -errno;
        ALOGE("Unable to configure 40 handler: %s\n", strerror(errno));
        return err;
    }

    if (sigaction(41, &act, &gOrigSigactionStart) != 0) {
        int err = -errno;
        ALOGE("Unable to configure 40 handler: %s\n", strerror(errno));
        return err;
    }

    return 0;
}

int main(int argc, char** argv) {

    printf("starting dpcameratest\n");

    /*init for binder*/
    sp<android::ProcessState> proc(android::ProcessState::self());
    sp<android::IServiceManager> sm = android::defaultServiceManager();
    pCameramodule = new CameraModuleManager();
    if (pCameramodule->startModule(DP_DEVICE_0) == true) {
        printf("start dp device 0 success\n");
    } else {
        printf("start dp device 0 falied\n");
    }
    if (pCameramodule->startModule(DP_DEVICE_1) == true) {
        printf("start dp device 1 success\n");
    } else {
        printf("start dp device 1 falied\n");
    }
    configureSignals();
    /*start binder thread*/
    android::ProcessState::self()->startThreadPool();
    android::IPCThreadState::self()->joinThreadPool();
}
