#include "CameraManager.h"
#include "CameraModuleManager.h"

std::shared_ptr <android::yvr::CameraModuleManager> gCameraModuleManager;


int InitCameraDevice(){
    int dwRet = 0;
    gCameraModuleManager = std::make_shared<android::yvr::CameraModuleManager>();

    return dwRet;
}

int SetCameraSaveFile(android::yvr::DPDeviceType deviceType, bool bsave){
    int dwRet = 0;

    gCameraModuleManager->SaveFile(deviceType, bsave);

    return dwRet;
}

int SetCameraResolve(android::yvr::DPDeviceType deviceType, int capwidth, int capheight){
    int dwRet = 0;

    gCameraModuleManager->SetResolve(deviceType, capwidth, capheight);

    return dwRet;
}

int StartCameraDevice(android::yvr::DPDeviceType deviceType, android::yvr::CameraListener *listener){
    int dwRet = 0;

    gCameraModuleManager->startModule(deviceType, listener);

    return dwRet;

}

int StopCameraDevice(android::yvr::DPDeviceType deviceType){
    int dwRet = 0;
    gCameraModuleManager->stopModule(deviceType);

    return dwRet;

}
