/**
 * @file CameraBufferManager.h
 * <AUTHOR> (h<PERSON><PERSON><PERSON>@yvr.com)
 * @brief 
 * @version 0.1
 * @date 2023-09-11
 * 
 * @copyright Copyright (c) 2023
 * 
 */

#ifndef CAMERA_BUFFER_BUFFER_MANAGER_H
#define CAMERA_BUFFER_BUFFER_MANAGER_H

#include <deque>
#include <mutex>
#include <android/hardware_buffer.h>
#include <cutils/native_handle.h>
#include <utils/RefBase.h>
#include "CameraDef.h"

struct CameraBufferInfo {
    CameraAHBuffer* buffers;
    uint32_t streamNum;
    uint32_t bufferIndex;
};

struct BufferItemDesc {
    uint32_t width;
    uint32_t height;
    uint64_t usage;
    uint32_t format;
};

struct CameraBufferDesc {
    BufferItemDesc* items;
    uint32_t streamNum;
    uint32_t queueSize;
};

class CameraBufferManager : public android::RefBase {
 public:
    CameraBufferManager();
    ~CameraBufferManager();
    void allocateBuffer(CameraBufferDesc& desc);
    void freeBuffers(void);
    CameraBufferInfo* dequeueBuffer();
    void queueBuffer(CameraBufferInfo* buffer);
    bool isAvailable();
    void reInitBufferQueue();
    uint32_t getQueueSize() {return mQueueSize;}
    uint32_t getStreamNum() {return mStreamNum;}
    CameraBufferInfo* getBufferQueue() {return mAllocatedBuffers;}

 private:
    std::mutex mBufMutex;
    uint32_t mQueueSize;
    uint32_t mStreamNum;
    CameraBufferInfo* mAllocatedBuffers;
    std::deque<CameraBufferInfo*> mBuffersFree;
    bool mIsAvailable;
};

#endif  // CAMERA_BUFFER_BUFFER_MANAGER_H

