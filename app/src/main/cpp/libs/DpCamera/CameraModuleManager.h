/**
 * @file CameraModuleManager.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2023-05-31
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#pragma once
#include <string>
#include "CameraDevice.h"
#include <android/hardware/camera/device/3.2/ICameraDevice.h>
#include <android/hardware/camera/provider/2.5/ICameraProvider.h>

namespace android {
namespace yvr {

struct VendorTagName {
    const char* sectionName;
    const char* tagName;
};

class CameraModuleManager : public virtual android::RefBase {
 public:
    CameraModuleManager();
    ~CameraModuleManager();
    int initalize();
    int8_t startModule(DPDeviceType deviceType);
    int8_t startModule(DPDeviceType deviceType, CameraListener *listener);
    int8_t stopModule(DPDeviceType deviceType);
    void SetResolve(DPDeviceType deviceType,int width, int height);
    void SaveFile(DPDeviceType deviceType, bool bsave);

 private:
    android::hardware::hidl_vec<android::hardware::hidl_string> getDeviceNames();
    android::sp<android::hardware::camera::device::V3_2::ICameraDevice> startDevice(const std::string& name);
    DPDeviceType getDPDeviceType(DPDeviceType deviceType, const char* name, android::sp<android::hardware::camera::device::V3_2::ICameraDevice> device);
    void loadVendorTagId();
    uint32_t getTagId(android::hardware::hidl_vec<hardware::camera::common::V1_0::VendorTagSection>& vts, const VendorTagName& tag);
    
    std::mutex mLock;
    bool mInitSuccess;
    android::sp<android::hardware::camera::provider::V2_4::ICameraProvider> mCameraProvider;
    bool mCameraStarted[DP_DEVICE_MAX];
    int mWidth[DP_DEVICE_MAX];
    int mHeight[DP_DEVICE_MAX];
    bool mSaveFile[DP_DEVICE_MAX];
    DefaultKeyedVector<std::string, android::sp<CameraDevice>> mCameraDevices[DP_DEVICE_MAX];
};

}  // namespace yvr
}  // namespace android
