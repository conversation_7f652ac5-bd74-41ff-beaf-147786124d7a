/**
 * @file CameraDevice.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2023-05-31
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#define LOG_TAG "DPCameraDevice"
#include "CameraDevice.h"
#include <vector>

namespace android {
namespace yvr {

extern uint32_t vTAGRCV;

using ::android::hardware::graphics::common::V1_0::PixelFormat;

CameraDevice::CameraDevice(DPDeviceType deviceType, sp<ICameraDevice> device, int width, int height, bool bsave) {
    mDeviceType = deviceType;
    mCameraDevice = device;
    mLastTimeStamp = 0;
    mSaveFile = bsave;

    switch(deviceType) {
        case DP_DEVICE_0: 
        case DP_DEVICE_1:{
            kPreferredWidth = width<<1;
            kPreferredHeight = height;
            kPreferredFormat = DP_DEVICE_0_FORMAT;
            mBufferWidth = width>>1;
            mBufferHeight = height* 10 / 8;
            mBufferFormat = DP_DEVICE_0_BUFFER_FORMAT;
            break;
        }
        // case DP_DEVICE_1: {
        //     kPreferredWidth = width;
        //     kPreferredHeight = height;
        //     kPreferredFormat = DP_DEVICE_1_FORMAT;
        //     mBufferWidth = width;
        //     mBufferHeight = height;
        //     mBufferFormat = DP_DEVICE_1_BUFFER_FORMAT;
        //     break;
        // }
        default:
            break;
    }

    ALOGE("[%d]CameraDevice kPreferredWidth:%d kPreferredHeight:%d mBufferWidth:%d mBufferHeight:%d", 
                mDeviceType, kPreferredWidth, kPreferredHeight, mBufferWidth, mBufferHeight);

    // mStatistics = std::make_shared<Statistics>(deviceType);
    // mStatistics->ResetAll();

    initCameraDevice();
    allocCameraBuffer();
}

CameraDevice::~CameraDevice() {
    ALOGI("CameraDevice close");

    // mStatistics->ResetAll();
    // mStatistics.reset();

    if (mStaticMetadata != nullptr) {
        free_camera_metadata(mStaticMetadata);
        mStaticMetadata = nullptr;
    }

    camera_metadata_t* settings;
    convertFromHidl(mMetadataSetting, (const camera_metadata_t**)&settings);
    if (settings != nullptr) {
        free_camera_metadata(settings);
        settings = nullptr;
    }
}

DPDeviceType CameraDevice::getDeviceType() {
    return mDeviceType;
}

void CameraDevice::initCameraDevice() {
    getCameraCharacteristics(mCameraDevice, &mStaticMetadata);
    isUseHalBufManager(mStaticMetadata);
    getRequestPartialResultCount(mStaticMetadata, &mSupportsPartialResults, &mPartialResultCount);
}

void CameraDevice::allocCameraBuffer() {
    CameraBufferDesc desc;
    BufferItemDesc itemDesc;
    itemDesc.width = mBufferWidth;
    itemDesc.height = mBufferHeight;
    itemDesc.usage = AHARDWAREBUFFER_USAGE_CPU_READ_OFTEN |AHARDWAREBUFFER_USAGE_CPU_WRITE_OFTEN;
    itemDesc.format = mBufferFormat;
    desc.items = &itemDesc;
    desc.streamNum = 1;
    desc.queueSize = BUFFER_SIZE;
    mBufferManager = new CameraBufferManager();
    mBufferManager->allocateBuffer(desc);
}

bool CameraDevice::configCameraStream() {
    HalStreamConfiguration halStreamConfig;
    V3_2::Stream previewStream;
    AvailableStream previewThreshold = {kPreferredWidth, kPreferredHeight, kPreferredFormat};
    createCameraSession(mCameraDevice, this, &mSession);
    if (mSession == NULL) {
        ALOGE("createCameraSession Failed, device:%d", mDeviceType);
        return false;
    }
    constructDefaultRequestSettings(mSession, mMetadataSetting);
    setRCVState();
    setTargetFPS(60);

    configurePreviewStream(mStaticMetadata, &previewThreshold, &mSession, &previewStream, &halStreamConfig, 0);
    mStreamId = halStreamConfig.streams[0].id;
    initResultMetadataQueue(mSession);
    ALOGI("configstreams");
    return true;
}

void CameraDevice::setTargetFPS(uint32_t fps) {
    camera_metadata_t *settings;
    uint32_t fpsRagge[2] = {fps, fps};

    convertFromHidl(mMetadataSetting, (const camera_metadata_t **) &settings);
    if (settings == NULL) {
        ALOGE("convertFromHidl failed");
        return;
    }

    setMetaDataEntry(settings, ANDROID_CONTROL_AE_TARGET_FPS_RANGE, (const void *) &fpsRagge, 2);
}

void CameraDevice::setRCVState() {
    camera_metadata_t *settings;
    uint8_t rcv = 1;

    convertFromHidl(mMetadataSetting, (const camera_metadata_t **) &settings);
    if (settings == NULL) {
        ALOGE("convertFromHidl failed");
        return;
    }

    setMetaDataEntry(settings, vTAGRCV, (const void *) &rcv, 1);
}

void CameraDevice::setMetaDataEntry(camera_metadata_t *settings, uint32_t tag, const void *data, size_t size) {
    int res;
    camera_metadata_entry_t entry;
    res = find_camera_metadata_entry(settings, tag, &entry);
    if (res == NAME_NOT_FOUND) {
        res = add_camera_metadata_entry(settings, tag, data, size);
    } else if (res == OK) {
        res = update_camera_metadata_entry(settings, entry.index, data, size, NULL);
    }

    if (res != OK) {
        ALOGE("setMetaDataEntry failed,entry:%x", tag);
    }
}

bool CameraDevice::startCamera() {
    mRequestThread = new RequestThread(this);
    mRequestThread->run("CamRequst");
    ALOGI("startCamera");
    return true;
}

void CameraDevice::stopCamera() {
    if (mRequestThread != NULL) {
        mRequestThread->requestExit();
        mRequestThread->join();
        mRequestThread.clear();
    }

    if (mSession != NULL) {
        auto status = mSession->flush();
        if (!status.isOk()) {
            ALOGE("flush failed");
        }
        auto status1 = mSession->close();
        if (!status1.isOk()) {
            ALOGE("close failed");
        }
        mSession.clear();  // release ref
    }
    ALOGI("stopCamera");
}

void CameraDevice::createCameraSession(sp<ICameraDevice> device, sp<V3_5::ICameraDeviceCallback> cb, sp<ICameraDeviceSession>* session) {
    Return<void> ret;
    ret = device->open(cb, [&](auto status, const auto& newSession) {
        if (status != Status::OK || newSession == nullptr) {
            ALOGE("createCameraSession failed, status:%d", (int)status);
        }
        if (status != Status::OK) {
            ALOGE("createCameraSession is not Ok");
        }
        *session = newSession;
    });
}

Return<void> CameraDevice::processCaptureResult_3_4(const hidl_vec<V3_4::CaptureResult>& results) {
    for (size_t i = 0; i < results.size(); i++) {
        processCaptureResultLocked(results[i].v3_2, results[i].physicalCameraMetadata);
    }

    return Void();
}

Return<void> CameraDevice::processCaptureResult(const hidl_vec<CaptureResult>& results) {
    hidl_vec<PhysicalCameraMetadata> noPhysMetadata;
    for (size_t i = 0; i < results.size(); i++) {
        processCaptureResultLocked(results[i], noPhysMetadata);
    }

    return Void();
}


uint64_t getBtimerNs() {
    struct timespec tp;

    const int status = clock_gettime(CLOCK_BOOTTIME, &tp);
    if (status != 0) {
      //   YLOGE("!! clock get time failed !!");
        return 0;
    }
    const uint64_t result = (uint64_t)tp.tv_sec * (uint64_t)(1000 * 1000 * 1000) + uint64_t(tp.tv_nsec);
    return result;
}

bool CameraDevice::processCaptureResultLocked(const CaptureResult& results, hidl_vec<PhysicalCameraMetadata> physicalCameraMetadata) {
    bool notify = false;
    uint32_t frameNumber = results.frameNumber;
    
    uint64_t timebegin = getBtimerNs();

    if ((results.result.size() == 0) && (results.outputBuffers.size() == 0) && (results.inputBuffer.buffer == nullptr) &&
        (results.fmqResultSize == 0)) {
        ALOGE("No result data provided by HAL for frame %d result count: %d", frameNumber, (int)results.fmqResultSize);
        return notify;
    }

    std::unique_lock<std::mutex> l(mLock);
    ssize_t idx = mInflightMap.indexOfKey(frameNumber);
    if (NAME_NOT_FOUND == idx) {
        ALOGE("Unexpected frame number! received: %u", frameNumber);
        return notify;
    }

    InFlightRequest& request = mInflightMap.editValueAt(idx);

    CameraMetadata resultMetadata;
    size_t resultSize = 0;

    if (results.fmqResultSize > 0) {
        resultMetadata.resize(results.fmqResultSize);
        if (mResultQueue == nullptr) {
            ALOGE("resultQueue is null");
            return notify;
        }

        if (!mResultQueue->read(resultMetadata.data(), results.fmqResultSize)) {
            ALOGE("Frame %d: Cannot read camera metadata from fmq, size = %" PRIu64, frameNumber, results.fmqResultSize);
            return notify;
        }

        std::vector<::android::hardware::camera::device::V3_2::CameraMetadata> physResultMetadata;
        physResultMetadata.resize(physicalCameraMetadata.size());
        for (size_t i = 0; i < physicalCameraMetadata.size(); i++) {
            physResultMetadata[i].resize(physicalCameraMetadata[i].fmqMetadataSize);
            if (!mResultQueue->read(physResultMetadata[i].data(), physicalCameraMetadata[i].fmqMetadataSize)) {
                ALOGE("Frame %d: Cannot read physical camera metadata from fmq, size = %" PRIu64, frameNumber,
                      physicalCameraMetadata[i].fmqMetadataSize);
                return notify;
            }
        }
        resultSize = resultMetadata.size();
    } else if (results.result.size() > 0) {
        resultMetadata.setToExternal(const_cast<uint8_t*>(results.result.data()), results.result.size());
        resultSize = resultMetadata.size();
    }

    if (!mSupportsPartialResults && (resultSize > 0) && (results.partialResult != 1)) {
        ALOGE("Result is malformed for frame %d: partial_result %u must be 1  if partial result is not supported", frameNumber,
              results.partialResult);
        return notify;
    }

    if ((mSupportsPartialResults == false && results.partialResult == 1) || (mSupportsPartialResults == true && results.partialResult == 2)) {
        getTimestampFromMetadata(resultMetadata, request.bTimestamp);
    }

    if (results.outputBuffers.size() > 0) {
        if (mSaveFile == true  )
        {
            dumpFileTest(request);
        }

        timebegin = getBtimerNs();
        if (mListener != nullptr) {
            //mListener->onFrameAvailable
            mListener->onFrameAvailable(request.bufferInfo->buffers[0], mDeviceType);
        }
        // uint64_t timeend = getBtimerNs();


        mBufferManager->queueBuffer(request.bufferInfo);

        // mStatistics->DpCameraFrame(request.bTimestamp - mLastTimeStamp, (timeend - timebegin) * 1e-6);

        // ALOGI("[%d]frame duration:%" PRIu64 ", frame id:%d, duration:%fms", mDeviceType, (request.bTimestamp - mLastTimeStamp), frameNumber, (timeend - timebegin) * 1e-6);
        
        mLastTimeStamp = request.bTimestamp;
        mInflightMap.removeItemsAt(idx);
    }

    return notify;
}

void CameraDevice::dumpFileTest(InFlightRequest& request) {
    if (mFile == 0)
    {
        std::string file;
        if (mDeviceType == DP_DEVICE_1)
        {
            file = "/sdcard/dpframe_" + std::to_string(kPreferredWidth/2) + "x"  + std::to_string(kPreferredHeight) + ".yuv";
        } else {
            file = "/sdcard/dpframe_" + std::to_string(kPreferredWidth/2) + "x"  + std::to_string(kPreferredHeight) + ".uyvy";
        }
        mFile = open(file.c_str(), O_CREAT | O_RDWR, 0644);
    }
    
    if (mFile > 0) {
        mFileNum++;
        if (mFileNum >= 3000)
        {
            ALOGI("[%d]frame reopen:size: %d", mDeviceType, request.bufferInfo->buffers[0].size);
            mFileNum = 0;
            close(mFile);
            mFile = 0;
        }else
        {
            // reopen file spend long time cause "no free buffer"
            write(mFile, request.bufferInfo->buffers[0].vaddr, kPreferredWidth* kPreferredHeight);
        }
        
    }
}

void CameraDevice::setListener(CameraListener *listener)
{
    mListener = listener;
}

void CameraDevice::processCaptureRequest(void) {
    StreamBuffer outputBuffer;
    InFlightRequest inflightReq;
    CameraBufferInfo* bufferInfo = mBufferManager->dequeueBuffer();

    if (bufferInfo == NULL) {
        // buffer is all inflight, sleep and wait
        usleep(10000);
        mBufferManager->reInitBufferQueue();
        ALOGE("[%d]no free buffer", mDeviceType);
        return;
    }

    outputBuffer.bufferId = bufferInfo->bufferIndex;
    outputBuffer.buffer = (native_handle_t*)bufferInfo->buffers[0].handle;
    outputBuffer.streamId = mStreamId;
    outputBuffer.status = BufferStatus::OK;
    outputBuffer.acquireFence = nullptr;
    outputBuffer.releaseFence = nullptr;

    hidl_vec<StreamBuffer> outputBuffers = {outputBuffer};
    StreamBuffer emptyInputBuffer = {-1, 0, nullptr, BufferStatus::ERROR, nullptr, nullptr};
    CaptureRequest request = {mframeNumber, 0 /* fmqSettingsSize */, mMetadataSetting, emptyInputBuffer, outputBuffers};
    {
        std::unique_lock<std::mutex> l(mLock);
        inflightReq.bufferInfo = bufferInfo;
        inflightReq.frameNumber = mframeNumber;
        mInflightMap.add(mframeNumber, inflightReq);
    }

    Status status = Status::INTERNAL_ERROR;
    uint32_t numRequestProcessed = 0;
    hidl_vec<BufferCache> cachesToRemove;

    Return<void> returnStatus = mSession->processCaptureRequest({request}, cachesToRemove, [&status, &numRequestProcessed](auto s, uint32_t n) {
        status = s;
        numRequestProcessed = n;
    });

    if (!returnStatus.isOk() || status != Status::OK) {
        std::unique_lock<std::mutex> l(mLock);
        mBufferManager->queueBuffer(inflightReq.bufferInfo);
        ssize_t idx = mInflightMap.indexOfKey(mframeNumber);
        if (NAME_NOT_FOUND != idx) {
            mInflightMap.removeItemsAt(idx);
        }
        ALOGE("request failed, frame: %d, status:%d, device: %d", mframeNumber, status, mDeviceType);
    }

    mframeNumber++;
}

void CameraDevice::getCameraCharacteristics(sp<ICameraDevice> device, camera_metadata_t** staticMeta) {
    Return<void> ret = device->getCameraCharacteristics([&](Status s, CameraMetadata metadata) {
        *staticMeta = clone_camera_metadata(reinterpret_cast<const camera_metadata_t*>(metadata.data()));
        if (s != Status::OK) {
            ALOGE("getCameraCharacteristics failed");
        }
    });

    if (!ret.isOk()) {
        ALOGE("getCameraCharacteristics is not Ok");
    }
}

bool CameraDevice::isUseHalBufManager(camera_metadata_t* metadata) {
    bool useHalBufManager = false;
    camera_metadata_ro_entry entry;
    auto status = find_camera_metadata_ro_entry(metadata, ANDROID_INFO_SUPPORTED_BUFFER_MANAGEMENT_VERSION, &entry);
    if ((0 == status) && (entry.count == 1)) {
        useHalBufManager = (entry.data.u8[0] == ANDROID_INFO_SUPPORTED_BUFFER_MANAGEMENT_VERSION_HIDL_DEVICE_3_5);
    }

    ALOGI("isUseHalBufManager:%d", useHalBufManager);
    return useHalBufManager;
}

Status CameraDevice::getJpegBufferSize(camera_metadata_t* metadata, uint32_t* outBufSize) {
    if (nullptr == metadata || nullptr == outBufSize) {
        return Status::ILLEGAL_ARGUMENT;
    }

    camera_metadata_ro_entry entry;
    int rc = find_camera_metadata_ro_entry(metadata, ANDROID_JPEG_MAX_SIZE, &entry);
    if ((0 != rc) || (1 != entry.count)) {
        return Status::ILLEGAL_ARGUMENT;
    }

    *outBufSize = static_cast<uint32_t>(entry.data.i32[0]);
    return Status::OK;
}

void CameraDevice::getRequestPartialResultCount(camera_metadata_t* metadata, bool* supportsPartialResults, uint32_t* partialResultCount) {
    camera_metadata_ro_entry entry;
    auto status = find_camera_metadata_ro_entry(metadata, ANDROID_REQUEST_PARTIAL_RESULT_COUNT, &entry);
    if ((0 == status) && (entry.count > 0)) {
        *partialResultCount = entry.data.i32[0];
        *supportsPartialResults = (*partialResultCount > 1);
    }
}

void CameraDevice::initResultMetadataQueue(sp<ICameraDeviceSession> session) {
    std::shared_ptr<ResultMetadataQueue> resultQueue;
    auto resultQueueRet = session->getCaptureResultMetadataQueue([&resultQueue](const auto& descriptor) {
        resultQueue = std::make_shared<ResultMetadataQueue>(descriptor);
        if (!resultQueue->isValid() || resultQueue->availableToWrite() <= 0) {
            ALOGE("%s: HAL returns empty result metadata fmq, not use it", __func__);
            resultQueue = nullptr;
        }
    });

    if (!resultQueueRet.isOk()) {
        ALOGE("getCaptureResultMetadataQueue is not Ok");
    }

    mResultQueue = resultQueue;
}

void CameraDevice::constructDefaultRequestSettings(sp<ICameraDeviceSession> session, CameraMetadata& settings) {
    camera_metadata_t* buffer;
    RequestTemplate reqTemplate = RequestTemplate::PREVIEW;
    Return<void> ret;
    ret = session->constructDefaultRequestSettings(reqTemplate, [&](auto status, const auto& req) {
        if (status != Status::OK) {
            ALOGE("constructDefaultRequestSettings failed");
        }
        buffer = clone_camera_metadata(reinterpret_cast<const camera_metadata_t*>(req.data()));
        convertToHidl(buffer, &settings);
    });

    if (!ret.isOk()) {
        ALOGE("constructDefaultRequestSettings is not Ok");
    }
}

void CameraDevice::configurePreviewStream(camera_metadata_t* metadata, const AvailableStream* previewThreshold,
                                          sp<ICameraDeviceSession>* session /*out*/, V3_2::Stream* previewStream /*out*/,
                                          HalStreamConfiguration* halStreamConfig /*out*/, uint32_t streamConfigCounter) {
    Return<void> ret;
    sp<V3_5::ICameraDeviceSession> session3_5 = V3_5::ICameraDeviceSession::castFrom(*session);
    uint64_t usage = 0x00000003;  // CPU_READ_OFTEN

    uint32_t jpegBufferSize = 0;
    getJpegBufferSize(metadata, &jpegBufferSize);

    V3_2::Stream stream3_2 = {0,
                              StreamType::OUTPUT,
                              static_cast<uint32_t>(kPreferredWidth),
                              static_cast<uint32_t>(kPreferredHeight),
                              static_cast<android::hardware::graphics::common::V1_0::PixelFormat>(kPreferredFormat),
                              usage,
                              static_cast<V3_2::DataspaceFlags>(Dataspace::JFIF),
                              StreamRotation::ROTATION_0};

    hidl_vec<V3_2::Stream> streams3_2 = {stream3_2};
    ::android::hardware::camera::device::V3_5::StreamConfiguration config3_5;
    createStreamConfiguration(streams3_2, StreamConfigurationMode::NORMAL_MODE, &config3_5, jpegBufferSize);
    if (session3_5 != nullptr) {
        config3_5.v3_4.sessionParams = mMetadataSetting;
        config3_5.streamConfigCounter = streamConfigCounter;
        // if (mDeviceType == DP_DEVICE_1) {
        //     //IPE by pass
        //     config3_5.v3_4.operationMode = (::android::hardware::camera::device::V3_2::StreamConfigurationMode)0xF100;
        // }
        ret = session3_5->configureStreams_3_5(config3_5, [&](Status s, V3_4::HalStreamConfiguration halConfig) {
            if (s != Status::OK) {
                ALOGE("configureStreams_3_5 failed");
            }
            halStreamConfig->streams.resize(1);
            halStreamConfig->streams[0] = halConfig.streams[0].v3_3.v3_2;
        });
    }
    *previewStream = stream3_2;
}

Status CameraDevice::getAvailableOutputStreams(camera_metadata_t* staticMeta, std::vector<AvailableStream>& outputStreams,
                                               const AvailableStream* threshold) {
    AvailableStream depthPreviewThreshold = {kPreferredWidth, kPreferredHeight, kPreferredFormat};
    if (nullptr == staticMeta) {
        return Status::ILLEGAL_ARGUMENT;
    }

    camera_metadata_ro_entry scalarEntry;
    camera_metadata_ro_entry depthEntry;
    int foundScalar = find_camera_metadata_ro_entry(staticMeta, ANDROID_SCALER_AVAILABLE_STREAM_CONFIGURATIONS, &scalarEntry);
    int foundDepth = find_camera_metadata_ro_entry(staticMeta, ANDROID_DEPTH_AVAILABLE_DEPTH_STREAM_CONFIGURATIONS, &depthEntry);
    if ((0 != foundScalar || (0 != (scalarEntry.count % 4))) && (0 != foundDepth || (0 != (depthEntry.count % 4)))) {
        return Status::ILLEGAL_ARGUMENT;
    }

    if (foundScalar == 0 && (0 == (scalarEntry.count % 4))) {
        fillOutputStreams(&scalarEntry, outputStreams, threshold, ANDROID_SCALER_AVAILABLE_STREAM_CONFIGURATIONS_OUTPUT);
    }

    if (foundDepth == 0 && (0 == (depthEntry.count % 4))) {
        fillOutputStreams(&depthEntry, outputStreams, &depthPreviewThreshold, ANDROID_DEPTH_AVAILABLE_DEPTH_STREAM_CONFIGURATIONS_OUTPUT);
    }

    return Status::OK;
}

void CameraDevice::fillOutputStreams(camera_metadata_ro_entry_t* entry, std::vector<AvailableStream>& outputStreams, const AvailableStream* threshold,
                                     const int32_t availableConfigOutputTag) {
    for (size_t i = 0; i < entry->count; i += 4) {
        if (availableConfigOutputTag == entry->data.i32[i + 3]) {
            if (nullptr == threshold) {
                AvailableStream s = {entry->data.i32[i + 1], entry->data.i32[i + 2], entry->data.i32[i]};
                outputStreams.push_back(s);
            } else {
                if ((threshold->format == entry->data.i32[i]) && (threshold->width >= entry->data.i32[i + 1]) &&
                    (threshold->height >= entry->data.i32[i + 2])) {
                    AvailableStream s = {entry->data.i32[i + 1], entry->data.i32[i + 2], threshold->format};
                    outputStreams.push_back(s);
                }
            }
        }
    }
}

void CameraDevice::createStreamConfiguration(const ::android::hardware::hidl_vec<V3_2::Stream>& streams3_2, StreamConfigurationMode configMode,
                                             ::android::hardware::camera::device::V3_5::StreamConfiguration* config3_5 /*out*/,
                                             uint32_t jpegBufferSize) {
    hidl_vec<V3_4::Stream> streams3_4(streams3_2.size());
    size_t idx = 0;
    for (auto& stream3_2 : streams3_2) {
        V3_4::Stream stream;
        stream.v3_2 = stream3_2;
        stream.bufferSize = 0;
        if (stream3_2.format == PixelFormat::BLOB && stream3_2.dataSpace == static_cast<V3_2::DataspaceFlags>(Dataspace::V0_JFIF)) {
            stream.bufferSize = jpegBufferSize;
        }
        streams3_4[idx++] = stream;
    }
    *config3_5 = {{streams3_4, configMode, {}}, 0};
}

void CameraDevice::getTimestampFromMetadata(CameraMetadata& metadata, uint64_t& btimestamp) {
    int res = OK;
    camera_metadata_entry_t entry;
    camera_metadata_t* settings;
    convertFromHidl(metadata, (const camera_metadata_t**)&settings);
    if (settings == NULL) {
        ALOGE("convertFromHidl failed");
        return;
    }

    res = find_camera_metadata_entry(settings, ANDROID_SENSOR_TIMESTAMP, &entry);
    if (res == OK) {
        if (entry.count > 0) {
            btimestamp = ((uint64_t)(*entry.data.i64)) / 1000;
        } else {
            ALOGE("gettimestamp failed");
        }
    }
}

Return<void> CameraDevice::notify(const hidl_vec<NotifyMsg>& messages) {
    for (size_t i = 0; i < messages.size(); i++) {
        switch (messages[i].type) {
            case MsgType::ERROR:
                if (messages[i].msg.error.errorCode == ErrorCode::ERROR_BUFFER) {
                    std::unique_lock<std::mutex> l(mLock);
                    ssize_t idx = mInflightMap.indexOfKey(messages[i].msg.error.frameNumber);
                    if (NAME_NOT_FOUND != idx) {
                        InFlightRequest& request = mInflightMap.editValueAt(idx);
                        mBufferManager->queueBuffer(request.bufferInfo);
                        mInflightMap.removeItemsAt(idx);
                    }
                }
                ALOGE("[%d]camera frame error:%d, frame:%d, camera:%d", mStreamId, (int)messages[i].msg.error.errorCode, messages[i].msg.error.frameNumber,
                      mDeviceType);
                break;
            case MsgType::SHUTTER:
                break;
            default:
                ALOGE("Unsupported notify message %d", messages[i].type);
                break;
        }
    }
    return Void();
}

Return<void> CameraDevice::requestStreamBuffers(const hidl_vec<V3_5::BufferRequest>& bufReqs, requestStreamBuffers_cb _hidl_cb) {
    hidl_vec<StreamBufferRet> bufRets;
    ALOGE("Camera does not support HAL buffer management");
    _hidl_cb(BufferRequestStatus::FAILED_ILLEGAL_ARGUMENTS, bufRets);
    return Void();
}

Return<void> CameraDevice::returnStreamBuffers(const hidl_vec<StreamBuffer>& buffers) {
    ALOGE("Camera does not support HAL buffer management at return");
    return Void();
}

bool CameraDevice::convertFromHidl(const CameraMetadata& src, const camera_metadata_t** dst) {
    if (src.size() == 0) {
        // Special case for null metadata
        *dst = nullptr;
        return true;
    }

    const uint8_t* data = src.data();
    // sanity check the size of CameraMetadata match underlying camera_metadata_t
    if (get_camera_metadata_size((camera_metadata_t*)data) != src.size()) {
        return false;
    }
    *dst = (camera_metadata_t*)data;
    return true;
}

// Note: existing data in dst will be gone. Caller still owns the memory of src
void CameraDevice::convertToHidl(const camera_metadata_t* src, CameraMetadata* dst) {
    if (src == nullptr) {
        return;
    }
    size_t size = get_camera_metadata_size(src);
    dst->setToExternal((uint8_t*)src, size);
    return;
}

CameraDevice::RequestThread::RequestThread(sp<CameraDevice> device) { mCameraDevice = device; }

CameraDevice::RequestThread::~RequestThread() { ALOGI("RequestThread destory"); }

bool CameraDevice::RequestThread::threadLoop() {
    mCameraDevice->processCaptureRequest();
    return true;
}

}  // namespace yvr

}  // namespace android
