#pragma once

#include <algorithm>
#include <stdint.h>
#include <thread>


class Statistics {
public:
	Statistics(int id) {
		mId = id;
		ResetAll();
		mbStatisThread = true;
    	mStatisThread = std::thread(&Statistics::Statics, this);
	}

	~Statistics(){
		mbStatisThread = false;
    	if (mStatisThread.joinable()) {
        	mStatisThread.join();
    	}
	}

	void Statics()
	{
		while (mbStatisThread)
		{
			CheckAndResetSecond();
			std::this_thread::sleep_for(std::chrono::milliseconds(10));
		}
	}

	void ResetAll() {
		m_current = 0;

		m_framesOpenglInSecond = 0;
		m_framesOpenglPrev = 0;
		m_OpenglLatencyTotalUs = 0;
		m_OpenglLatencyMax = 0;
		m_OpenglLatencyAveragePrev = 0;
		m_OpenglLatencyMaxPrev = 0;

		m_OpenglProTotalUs = 0;
		m_OpenglProMax = 0;
		m_OpenglProAveragePrev = 0;
		m_OpenglProMaxPrev = 0;

		m_framesAllInSecond = 0;
		m_framesAllPrev = 0;
		m_allLatencyTotalUs = 0;
		m_allLatencyMax = 0;
		m_allLatencyAveragePrev = 0;
		m_allLatencyMaxPrev = 0;
	}

	void DpCameraFrame(uint64_t latencyms, uint64_t latencymspro) {
		if (mSecondTotal <= 0)
		{
			return;
		}
		
		m_framesOpenglInSecond++;
		m_framesOpenglInSecondTotal++;
		m_OpenglLatencyAveragePrev += latencyms;
		m_OpenglLatencyTotalUs += latencyms;
		m_OpenglLatencyMax = std::max(latencyms, m_OpenglLatencyMax);
		m_OpenglProAveragePrev += latencymspro;
		m_OpenglProTotalUs += latencymspro;
		m_OpenglProMax = std::max(latencymspro, m_OpenglProMax);
	}

	void TotalOutput(uint64_t latencyUs) {
		if (mSecondTotal <= 0)
		{
			return;
		}
		
		m_framesAllInSecond++;
		m_framesAllInSecondTotal++;
		m_allLatencyAveragePrev += latencyUs;
		m_allLatencyTotalUs += latencyUs;
		m_allLatencyMax = std::max(latencyUs, m_allLatencyMax);
	}

	uint64_t GetOpenglLatencyAverage() {
		return m_OpenglLatencyAverageInSecond;
	}
	
	uint64_t GetOpenglFPS() {
		return m_framesOpenglPrev;
	}

	uint64_t GetAllLatencyAverage() {
		return m_allLatencyAverageInSecond;
	}
	float GetAllFPS() {
		return m_framesAllPrev;
	}

	void PrintfTotal()
	{

	}


private:
	void ResetSecond() {

		m_OpenglLatencyAverageInSecond = m_OpenglLatencyAveragePrev;
		m_OpenglLatencyAveragePrev = 0;
		m_framesOpenglPrev = m_framesOpenglInSecond;
		m_framesOpenglInSecond = 0;
		m_OpenglLatencyMaxPrev = m_OpenglLatencyMax;
		m_OpenglLatencyTotalUs = 0;
		m_OpenglLatencyMax = 0;

		m_OpenglProAverageInSecond = m_OpenglProAveragePrev;
		m_OpenglProAveragePrev = 0;
		m_OpenglProMaxPrev = m_OpenglProMax;
		m_OpenglProTotalUs = 0;
		m_OpenglProMax = 0;

		m_allLatencyAverageInSecond = m_allLatencyAveragePrev;
		m_allLatencyAveragePrev = 0;
		m_framesAllPrev = m_framesAllInSecond;
		m_framesAllInSecond = 0;
		m_allLatencyMaxPrev = m_allLatencyMax;
		m_allLatencyTotalUs = 0;
		m_allLatencyMax = 0;

	}

	void *CheckAndResetSecond() {
		auto duration = std::chrono::system_clock::now().time_since_epoch();
        uint64_t current = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count()/1000;
		if (0 == m_current)
		{
			m_current = current;
			mSecondTotal = -1;
			return nullptr;
		}
		

		if (m_current != current) {
			m_current = current;
			ResetSecond();

			if (mSecondTotal > 0)
			{
				ALOGE("[%d] statis frame num: %llu, cap: %.3f / max: %llu; pro: %.3f / max: %llu",
					 mId, (unsigned long long)m_framesOpenglPrev,
					 m_framesOpenglPrev==0?0:(double)GetOpenglLatencyAverage()/m_framesOpenglPrev,
					 (unsigned long long)m_OpenglLatencyMaxPrev,
					 m_framesOpenglPrev==0?0:(double)m_OpenglProAverageInSecond/m_framesOpenglPrev, 
					(unsigned long long)m_OpenglProMaxPrev);
			}

			mSecondTotal++;

			return nullptr;
		}else
		{
			return nullptr;
		}
	}

// opengl
	uint32_t m_framesOpenglInSecond;
	uint32_t m_framesOpenglInSecondTotal;
	uint32_t m_framesOpenglPrev;
	uint64_t m_OpenglLatencyTotalUs;
	uint64_t m_OpenglLatencyMax;
	uint64_t m_OpenglLatencyAveragePrev = 0;
	uint64_t m_OpenglLatencyAverageInSecond = 0;
	uint64_t m_OpenglLatencyMaxPrev;

	uint64_t m_OpenglProTotalUs;
	uint64_t m_OpenglProMax;
	uint64_t m_OpenglProAveragePrev = 0;
	uint64_t m_OpenglProAverageInSecond = 0;
	uint64_t m_OpenglProMaxPrev;


// all
	uint32_t m_framesAllInSecond;
	uint32_t m_framesAllInSecondTotal;
	uint32_t m_framesAllPrev;
	uint64_t m_allLatencyTotalUs;
	uint64_t m_allLatencyMax;
	uint64_t m_allLatencyAveragePrev = 0;
	uint64_t m_allLatencyAverageInSecond = 0;
	uint64_t m_allLatencyMaxPrev;

	uint64_t m_current;
	int mId;
	int mSecondTotal;

	bool        mbStatisThread;
    std::thread mStatisThread;
};