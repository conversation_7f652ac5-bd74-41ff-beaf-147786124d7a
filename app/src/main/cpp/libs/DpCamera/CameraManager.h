#pragma once


#ifndef PFDM_SCREENCAST_CAMERA_MANAGE_H
#define PFDM_SCREENCAST_CAMERA_MANAGE_H

#include "CameraDef.h"

int InitCameraDevice();
int SetCameraResolve(android::yvr::DPDeviceType deviceType, int capwidth, int capheight);
int StartCameraDevice(android::yvr::DPDeviceType deviceType, android::yvr::CameraListener *listener);
int StopCameraDevice(android::yvr::DPDeviceType deviceType);
int SetCameraSaveFile(android::yvr::DPDeviceType deviceType, bool bsave);

#endif // PFDM_SCREENCAST_CAMERA_MANAGE_H