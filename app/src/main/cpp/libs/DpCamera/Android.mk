LOCAL_PATH := $(call my-dir)

########################################################
               #DPCameraLIB#
########################################################

include $(CLEAR_VARS)
LOCAL_SRC_FILES := CameraDevice.cpp \
                   CameraModuleManager.cpp \
                   CameraManager.cpp \
                   CameraBufferManager.cpp \

LOCAL_SHARED_LIBRARIES := liblog libutils libcutils libbase libdl libui libbinder libhwbinder
LOCAL_SHARED_LIBRARIES += libcamera_metadata \
                        libfmq \
                        libhardware \
                        libhidlbase \
                        libhidltransport \
                        libnativewindow

LOCAL_STATIC_LIBRARIES := android.hardware.camera.common@1.0 \
                        android.hardware.camera.common@1.0-helper \
                        android.hardware.camera.provider@2.4 \
                        android.hardware.camera.provider@2.5 \
                        android.hardware.camera.device@1.0 \
                        android.hardware.camera.device@3.2 \
                        android.hardware.camera.device@3.3 \
                        android.hardware.camera.device@3.4 \
                        android.hardware.camera.device@3.5

LOCAL_MODULE:= libdpcamera

LOCAL_C_INCLUDES += system/media/private/camera/include
LOCAL_C_INCLUDES += vendor/qcom/opensource/commonsys-intf/display/gralloc
LOCAL_CFLAGS := -Wall -Wextra -Werror -Wunused -Wno-unused-parameter -Wno-unused-function

include $(BUILD_SHARED_LIBRARY)

########################################################
               #DPCameraTest#
########################################################

include $(CLEAR_VARS)
LOCAL_SRC_FILES := CameraDevice.cpp \
                   CameraModuleManager.cpp \
                   CameraBufferManager.cpp \
                   DPCameraTest.cpp

LOCAL_SHARED_LIBRARIES := liblog libutils libcutils libbase libdl libui libbinder libhwbinder
LOCAL_SHARED_LIBRARIES += libcamera_metadata \
                        libfmq \
                        libhardware \
                        libhidlbase \
                        libhidltransport \
                        libnativewindow

LOCAL_STATIC_LIBRARIES := android.hardware.camera.common@1.0 \
                        android.hardware.camera.common@1.0-helper \
                        android.hardware.camera.provider@2.4 \
                        android.hardware.camera.provider@2.5 \
                        android.hardware.camera.device@1.0 \
                        android.hardware.camera.device@3.2 \
                        android.hardware.camera.device@3.3 \
                        android.hardware.camera.device@3.4 \
                        android.hardware.camera.device@3.5

LOCAL_MODULE:= dpcameratest

LOCAL_C_INCLUDES += system/media/private/camera/include
LOCAL_C_INCLUDES += vendor/qcom/opensource/commonsys-intf/display/gralloc
LOCAL_CFLAGS := -Wall -Wextra -Werror -Wunused -Wno-unused-parameter -Wno-unused-function

include $(BUILD_EXECUTABLE)
