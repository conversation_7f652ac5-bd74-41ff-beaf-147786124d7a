//
// Created by <PERSON><PERSON><PERSON> on 2022/7/26.
//

#ifndef YVRNET_YVRNET_H
#define YVRNET_YVRNET_H

#include "yvrnet_common.h"

namespace yvrnet {

/**
 * 通用回调接口
 */
class IYvrnetCallback
{
public:
    virtual ~IYvrnetCallback() = default;

    /**
     * 错误通知
     * 
     * @param err 错误码
     * @param message 错误消息
     */
    virtual void onError(YvrnetErrorCode err, const char *message) {}

    /**
     * 事件通知
     *
     * @param event 事件
     */
    virtual void onEvent(const YvrnetEvent &event) {}

    /**
     * 启动通知
     * 
     * @param role 网络角色
     * @param success 是否启动成功
     */
    virtual void onStart(YvrnetNetworkRole role, bool success) {}

    /**
     * 停止通知
     * 
     * @param role 网络角色
     */
    virtual void onStop(YvrnetNetworkRole role) {}

    /**
     * 连接状态通知
     * 
     * @param connectStatus 连接状态
     */
    virtual void onConnectStatus(YvrnetConnectionStatus status) {}

    /**
     * 统计信息通知
     * 
     * @param statistics 统计信息
     */
    virtual void onStatistics(const YvrnetStatistics *statistics) {}

    /**
     * 收到普通消息
     * 
     * @param message
     * @param length
     */
    virtual void onReceiveMessage(const char *message, int length) {}

    /**
     * 收到媒体包
     * 
     * @param packet 媒体包
     * @param streamIndex 流ID
     */
    virtual void onReceivePacket(const YvrnetMediaPacket *packet, int streamIndex) {}
};

class IYvrnet
{
public:
    virtual ~IYvrnet() = default;

    /**
     * 新增回调
     * 
     * @param callback 回调
     */
    virtual void addCallback(IYvrnetCallback *callback) = 0;

    /**
     * 移除回调
     * 
     * @param callback 回调
     */
    virtual void removeCallback(IYvrnetCallback *callback) = 0;

    /**
     * 启动
     * 启动结果会通过 IYvrnetCallback::onStart 通知
     *
     * @param address
     *  - 服务端：监听网卡
     *  - 客户端：目标地址
     * @param port
     *  - 服务端：监听端口，如果为0，监听任意端口
     *  - 客户端：目标端口
     * @param bindPort
     *  - 服务端：返回监听的端口
     *  - 客户端：忽略该变量
     *
     * @return YVRNET_EC_OK 成功
     * @return 其它 失败
     */
    virtual YvrnetErrorCode start(const char *address, int port, int *bindPort = NULL) = 0;

    /**
     * 关闭
     * 关闭结果会通过 IYvrnetCallback::onStop 通知
     */
    virtual void stop() = 0;

    /**
     * 发送消息
     * 
     * @param message 消息内容
     * @param length 消息长度（字节）
     * @param reliable 是否可靠，如果为false，则有可能丢失
     * 
     * @return 成功发送的消息长度，0 代表发送失败
     */
    virtual int sendMessage(const char *message, int length, bool reliable = true) = 0;

    /**
     * 发送媒体包
     * 
     * @param packet 媒体包
     * @param streamIndex 流ID
     * 
     * @return true 发送成功
     * @return false 发送失败
     */
    virtual bool sendPacket(const YvrnetMediaPacket *packet, int streamIndex = 0) = 0;

    /**
     * 查询连接状态
     * 
     * @return true 已连接
     * @return false 未连接
     */
    virtual bool isConnected() = 0;
};

} // namespace yvrnet

extern "C" {

/**
 * 获取网络实例，必须先调用 yvrnet_startup 初始化网络，程序退出时调用 yvrnet_shutdown 释放资源
 * @return 网络实例
 */
YVRNET_API yvrnet::IYvrnet *yvrnet_getInstance(YvrnetInstance instance);

} // extern "C"

#endif //YVRNET_YVRNET_H
