#ifndef SCREENCASTSINK_SCREENCASTSINK_H
#define SCREENCASTSINK_SCREENCASTSINK_H


#include <jni.h>
#include <android/native_window_jni.h>
#include <android/native_window.h>
#include <string>
#include "Common.h"
#include "StreamerSink.h"

class ScreencastSink {
public:
    class StreamerSinkCallback : public StreamerSink::Callback {
    public:
        StreamerSinkCallback(DeviceID id, ScreencastSink *sink) : mID(id), mSink(sink) {}

        void onVideoSizeChanged(int width, int height) override;

        void onConnectionChanged(bool connected) override;

    private:
        const DeviceID mID;
        ScreencastSink *mSink;
    };

public:
    static ScreencastSink &instance() {
        static ScreencastSink pInstance;
        return pInstance;
    }

    ~ScreencastSink();

    void init(JNIEnv *env);

    void clear(JNIEnv *env);

    void start(JNIEnv *env, int id);

    void stop(JNIEnv *env, int id);

    bool isRunning(int id);

    bool isStreaming(int id);

    void setFocused(bool focused);

    void setSurface(JNIEnv *env, int id, jobject surface);

    void setSurfaceTexture(JNIEnv *env, int id, jobject surfaceTexture);

    void setTexture(int id, int texture);

    void setVideoFrameRate(int id, int frameRate);

    void setAudioSampleRate(int id, int sampleRate);

    void setVideoFrameDropThreshold(int id, int threshold);

    void setAudioFrameDropThreshold(int id, int threshold);

    void setVideoIp(int id, std::string ip);

    void setVideoPort(int id, std::string port);

    void setAudioIp(int id, std::string ip);

    void setAudioPort(int id, std::string port);

    void setStatisticsLevel(int level);

    void setSourceBuildInfo(std::string version, long time);

    void setTraceLevel(int level);

    void onFrameAvailable(int id);

    void clearAvailable(int id);

protected:
    void onVideoSizeChanged(DeviceID id, int width, int height) const;

    void onConnectionChanged(DeviceID id, bool connected) const;

private:
    ScreencastSink();

private:
    AppContext mContext;
    StreamerSink::Config mConfig[DEVICE_ID_MAX];
    StreamerSink mStreamerSink[DEVICE_ID_MAX];
    StreamerSinkCallback *mDPCallback;
    StreamerSinkCallback *mHDMICallback;
    jmethodID mOnStateChangedMethod;
    jmethodID mOnVideoSizeChangedMethod;
};


#endif //SCREENCASTSINK_SCREENCASTSINK_H
