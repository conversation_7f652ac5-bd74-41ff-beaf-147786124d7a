plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.pfdm.screencastsink.sinkremotesdk'
    compileSdk 34

    defaultConfig {
        minSdk 29

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        aidl true
    }
    android.libraryVariants.configureEach { variant ->
        variant.outputs.all {
            if (outputFileName.endsWith('.aar')) {
                outputFileName = "screencastsinksdk-${variant.name}.aar"
            }
        }
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation project(':sinknative')
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}